import React, { useState } from 'react';
import { Button } from 'antd-mobile';
import ChatDialog from './index';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

const ChatDialogExample: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'assistant',
      content: '你好，我是您的AI助手。请简单描述一下您的问题，我会为您提供专业的建议和分析。',
      timestamp: Date.now() - 60000
    }
  ]);
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [progress, setProgress] = useState(25);

  // 模拟AI回复
  const simulateAIResponse = (userMessage: string) => {
    setLoading(true);
    
    // 模拟网络延迟
    setTimeout(() => {
      const responses = [
        '我理解您的问题。让我为您分析一下具体情况...',
        '根据您提供的信息，我建议采取以下措施：\n\n1. 首先检查基础设施\n2. 确认安全规范\n3. 制定应急预案',
        '这是一个很好的问题。基于我的分析，建议您：\n\n**立即处理：**\n- 停止相关操作\n- 通知相关人员\n\n**后续跟进：**\n- 制定整改方案\n- 定期检查验收',
        '感谢您的提问。我已经为您生成了详细的分析报告，包含了风险评估和改进建议。'
      ];
      
      const randomResponse = responses[Math.floor(Math.random() * responses.length)];
      
      const newMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'assistant',
        content: randomResponse,
        timestamp: Date.now()
      };
      
      setMessages(prev => [...prev, newMessage]);
      setLoading(false);
      
      // 模拟进度更新
      if (currentStep < 3) {
        setCurrentStep(prev => prev + 1);
        setProgress(prev => Math.min(prev + 25, 100));
      }
    }, 1500);
  };

  // 发送消息
  const handleSendMessage = (message: string) => {
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: Date.now()
    };

    setMessages(prev => [...prev, userMessage]);

    // 模拟AI回复
    simulateAIResponse(message);
  };

  // 处理语音消息
  const handleVoiceMessage = (audioBlob: Blob) => {
    // 模拟语音转文字
    const voiceMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: '[语音消息] 这是一条模拟的语音转文字内容',
      timestamp: Date.now()
    };

    setMessages(prev => [...prev, voiceMessage]);

    // 模拟AI回复
    simulateAIResponse('语音消息');
  };

  // 重置对话
  const resetChat = () => {
    setMessages([
      {
        id: '1',
        type: 'assistant',
        content: '你好，我是您的AI助手。请简单描述一下您的问题，我会为您提供专业的建议和分析。',
        timestamp: Date.now()
      }
    ]);
    setCurrentStep(1);
    setProgress(25);
    setLoading(false);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>ChatDialog 组件示例</h2>
      <div style={{ marginBottom: '20px' }}>
        <Button 
          color="primary" 
          onClick={() => setVisible(true)}
          style={{ marginRight: '10px' }}
        >
          打开对话
        </Button>
        <Button 
          color="default" 
          onClick={resetChat}
        >
          重置对话
        </Button>
      </div>
      
      <div style={{ marginBottom: '20px', fontSize: '14px', color: '#666' }}>
        <p><strong>功能特点：</strong></p>
        <ul>
          <li>✅ 渐变色头部设计</li>
          <li>✅ 进度条和步骤指示</li>
          <li>✅ 对话气泡样式</li>
          <li>✅ 支持Markdown内容渲染</li>
          <li>✅ 语音录制功能</li>
          <li>✅ 语音输入模式切换</li>
          <li>✅ 打字效果加载动画</li>
          <li>✅ 自动滚动到底部</li>
          <li>✅ 字符计数显示</li>
          <li>✅ 移动端触摸适配</li>
          <li>✅ 可配置显示选项</li>
        </ul>
      </div>

      <ChatDialog
        visible={visible}
        onClose={() => setVisible(false)}
        title="智能分析助手"
        progress={progress}
        progressSteps={["问题描述", "对话分析", "建议生成", "方案优化"]}
        currentStep={currentStep}
        messages={messages}
        onSendMessage={handleSendMessage}
        onVoiceMessage={handleVoiceMessage}
        loading={loading}
        placeholder="请描述您遇到的问题..."
        maxLength={300}
        showVoiceButton={true}
        showProgress={true}
      />
    </div>
  );
};

export default ChatDialogExample;
