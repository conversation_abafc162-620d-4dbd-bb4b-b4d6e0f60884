/*
 * @Description: 检查项目详情页面
 * @Author: AI Assistant
 * @Date: 2025-06-27
 */
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button, Toast } from 'antd-mobile';
import { Select } from 'antd';
import { LeftOutline, PlayOutline, SoundOutline, CameraOutline, CloseOutline } from 'antd-mobile-icons';
import FormSubmission from '@/components/FormSubmission';
import PhotoCapture from '@/components/PhotoCapture';
import { SpeechRecognitionContainer } from '@/containers';
import { beforeUpload, createEventSourceHandler, getReqId } from '@/util/method';
import { apis } from '@/api/api';
import './CheckItemDetail.less';

// 检查要求接口
interface CheckRequirement {
  id: string;
  content: string;
  completed: boolean;
  value: string;
  material: string;
  imagUrl: string[];
}

// AI分析结果接口
interface AIAnalysisResult {
  id: string;
  requirement: string;
  result: string;
  count?: number;
}

// 上传状态类型
type UploadStatus = 'default' | 'photo' | 'voice';

const CheckItemDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [taskData, setTaskData] = useState<any>({});
  const [videoList, setVideoList] = useState<any[]>([]);
  const [currentVideoUrl, setCurrentVideoUrl] = useState<string>('');
  // 显示模式类型
  type DisplayMode = 'requirements' | 'analysis' | 'form';

  // 组件状态
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>('default');
  const [hasVoiceRecord, setHasVoiceRecord] = useState(false);
  const [showVoiceInput, setShowVoiceInput] = useState(false);
  const [displayMode, setDisplayMode] = useState<DisplayMode>('analysis');
  // PhotoCapture相关状态
  const [showPhotoCapture, setShowPhotoCapture] = useState(false);
  const [capturedImages, setCapturedImages] = useState<{ url: string, file: File }[]>([]);

  // 从taskData中获取检查要求数据
  const getCheckRequirements = (checkWay: number): CheckRequirement[] => {
    if (!taskData?.versions?.[0]?.results) return [];

    return taskData.versions[0].results.filter((result: any) => result.checkWay === checkWay).map((result: any) => ({
      id: result.id.toString(),
      content: result.config?.reqName || '',
      completed: false,
      value: result?.reqValue || '',
      material: result?.material || '',
      imagUrl: result?.imagUrl || []
    }));
  };

  const pngList = getCheckRequirements(0)

  const requirements = getCheckRequirements(1);



  // 根据checkWay判断是否显示特定按钮
  const shouldShowPhotoUpload = () => {
    if (!taskData?.checkWay) return true; // 默认显示
    return taskData.checkWay.includes(0);
  };
  // 根据checkWay判断是否显示语音识别按钮
  const shouldShowVoiceRecognition = () => {
    if (!taskData?.checkWay) return true; // 默认显示
    return taskData.checkWay.includes(1);
  };
  // 语音AI分析结果数据
  const [voiceAnalysisResults, setVoiceAnalysisResults] = useState<AIAnalysisResult[]>([]);

  // AI分析结果数据
  const [analysisResults, setAnalysisResults] = useState<AIAnalysisResult[]>([]);

  const handleBack = () => {
    navigate(-1);
  };

  // 处理语音录制
  const handleVoiceRecord = () => {
    // 显示语音输入组件
    setShowVoiceInput(true);
  };

  // 处理语音输入发送
  const handleVoiceSend = (speechText: string) => {
    console.log('语音识别文本:', speechText);
    const origin = window.location.origin;
    // 生成reqId 时间戳+随机4位数字
    const reqId = getReqId();

    const params = {
      type: 1,
      query: taskData.versions[0].id,
      executionId: JSON.parse(sessionStorage.getItem('prameData')).newId
    }
    let url = `${origin}/ginkgo/chat-api/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${taskData.versions[0].results[0].businessId}&question=${encodeURIComponent(speechText)}&params=${encodeURIComponent(JSON.stringify(params))}&scene=ai_check`;
    console.log('url', url);

    // 更新语音分析结果
    const eventSourceHandler = createEventSourceHandler({
      url,
      qaList: [],
      onMessage: (qaList: any[], answer: string) => {
        console.log('qaList', qaList);
        console.log('answer', answer);
      },
      onComplete: (qaList: any[], answerId: string, questionId: string) => {
        console.log('qaList', qaList);
        console.log('answerId', answerId);
        console.log('questionId', questionId);
        Toast.show('语音识别完成');
        getDetail()
      },
      onError: (error: any) => {
        console.log('error', error);
      }
    })
    eventSourceHandler.start();

    setVoiceAnalysisResults(analysisResults);
    setHasVoiceRecord(true);
    setShowVoiceInput(false);
    Toast.show('语音识别完成');
  };

  // 关闭语音输入
  const handleVoiceClose = () => {
    setShowVoiceInput(false);
  };

  // 打开拍照组件
  const handleOpenPhotoCapture = () => {
    setShowPhotoCapture(true);
  };

  // 关闭拍照组件
  const handleClosePhotoCapture = () => {
    setShowPhotoCapture(false);
  };

  // 处理拍照成功
  const handlePhotoCaptured = (file: File, dataUrl: string) => {
    // 添加到图片列表
    const newImage = { url: dataUrl, file };
    setCapturedImages(prev => [...prev, newImage]);

    beforeUpload(file, (res: any) => {
      console.log('res', res);
      const ids = res.map((item: any) => item.id).join(',');

      // 更新上传状态
      const origin = window.location.origin;
      // 生成reqId 时间戳+随机4位数字
      const reqId = getReqId();
      const params = {
        type: 0,
        query: taskData.versions[0].id,
        executionId: JSON.parse(sessionStorage.getItem('prameData')).newId
      }
      let url = `${origin}/ginkgo/chat-api/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${taskData.versions[0].results[0].businessId}&imageIds=${encodeURIComponent(ids)}&params=${encodeURIComponent(JSON.stringify(params))}&scene=ai_check`;
      console.log('url', url);
      // 更新语音分析结果
      const eventSourceHandler = createEventSourceHandler({
        url,
        qaList: [],
        onMessage: (qaList: any[], answer: string) => {
          console.log('qaList', qaList);
          console.log('answer', answer);
        },
        onComplete: (qaList: any[], answerId: string, questionId: string) => {
          console.log('qaList', qaList);
          console.log('answerId', answerId);
          console.log('questionId', questionId);
          Toast.show('图片识别完成');
          getDetail()
        },
        onError: (error: any) => {
          console.log('error', error);
        }
      })
      eventSourceHandler.start();
    });

    // 更新上传状态




    // // 更新上传状态
    // setUploadStatus('photo');

    // // 模拟AI分析
    // setTimeout(() => {
    //   setAnalysisResults([
    //     { id: '1', requirement: '记录消防水泵接合器数量', result: '118个' },
    //     { id: '2', requirement: '记录喷淋水泵接合器数量', result: '118个' }
    //   ]);
    //   Toast.show('AI分析完成');
    // }, 1000);

    // Toast.show('拍照成功');
  };

  // 删除图片
  const handleDeleteImage = (index: number) => {
    setCapturedImages(prev => prev.filter((_, i) => i !== index));
    // 如果没有图片了，重置状态
    if (capturedImages.length === 1) {
      setUploadStatus('default');
      setAnalysisResults([]);
    }
  };

  const handleOpenForm = () => {
    console.log('表单填报');
    // 如果当前已经是表单模式，则切换回检查要求，否则切换到表单模式
    setDisplayMode(displayMode === 'form' ? 'requirements' : 'form');
  };

  const handleSmartAnalysis = () => {
    console.log('智能分析');
    // 如果当前已经是分析模式，则切换回检查要求，否则切换到分析模式
    setDisplayMode(displayMode === 'analysis' ? 'requirements' : 'analysis');
  };


  const getDetail = () => {
    console.log('获取详情');
    const prameData = JSON.parse(sessionStorage.getItem('prameData') || '{}');
    apis.ginkgoSystem.getDetail({ id: prameData.newId, taskCode: prameData.taskCode }).then(res => {
      console.log('res', res);
      if (res.code === 0) {
        const placeList = res.data.placeList;
        const itemRespVOList = placeList.find((item: any) => item.id == sessionStorage.getItem('stageId'));
        const dataInfor = itemRespVOList.itemRespVOList.find((item: any) => item.configItemId == id);
        dataInfor.versions[0].results.forEach((item: any) => {
          item.material = item.material !== '' ? item.material.split(',') : [];
          if (item.material.length > 0) {
            apis.ginkgoUpload.getFileInfos({ ids: item.material }).then(res => {
              console.log('res', res);
              item.imagUrl = res.data.map((item: any) => item.url);
            });
          }
        });
        if(dataInfor.guidanceFileIds) {
          apis.ginkgoUpload.getFileInfos({ ids: dataInfor.guidanceFileIds.join(',') }).then(res => {
            setVideoList(res.data);
            // 默认显示第一个视频
            if (res.data && res.data.length > 0) {
              setCurrentVideoUrl(res.data[0].fileUrl);
            }
          })
        }
        setTaskData(dataInfor);
      }
    });
  }

  const onSubmit = (data: any) => {
    console.log('表单提交数据:', data);
      data.forEach((item, index) => {
        console.log(`项目${index + 1}:`, {
          id: item.id,
          value: item.value,
          isHazard: item.isHazard,
          imageCount: item.images.length
        });

      });
      const params = data.map((item: any) => ({
        id: item.id,
        value: item.value,
        isHazard: item.isHazard,
        material: item.material,
      }))
      apis.ginkgoSystem.requirementSubmit(params).then(res => {
        console.log('res', res);
        if (res.code === 0) {
          Toast.show('提交成功');
          getDetail()
          navigate(`/inspection-record/${id}`);
        } else {
          Toast.show(res.message);
        }
      })
  }

  useEffect(() => {
    const taskData = sessionStorage.getItem('checkItem');
    console.log('taskData', taskData);
    if (taskData) {
      const taskDataList = JSON.parse(taskData);
      if (taskDataList.configItemId == id) {
        taskDataList.versions[0].results.forEach(result => {
          result.material = result.material !== '' ? result.material.split(',') : [];
          if (result.material.length > 0) {
            apis.ginkgoUpload.getFileInfos({ ids: result.material }).then(res => {
              console.log('res', res);
              result.imagUrl = res.data.map((item: any) => item.url);
            });
          }
        });
        if(taskDataList.guidanceFileIds) {
          apis.ginkgoUpload.getFileInfos({ ids: taskDataList.guidanceFileIds.join(',') }).then(res => {
            setVideoList(res.data);
            // 默认显示第一个视频
            if (res.data && res.data.length > 0) {
              setCurrentVideoUrl(res.data[0].fileUrl);
            }
          })
        }

        setTaskData(taskDataList);
      }
    }
  }, []);

  return (
    <div className="check-item-detail-container">
      {/* 头部 */}
      <header className="check-item-header">
        <LeftOutline
          className="back-icon"
          onClick={handleBack}
        />
        <h1 className="page-title">{taskData.cateName}</h1>
      </header>

      {/* 主要内容区域 - 可滚动 */}
      <div className="main-content">
        <div className="section-header">
          <span className="section-icon guidance-icon"></span>
          <span className="section-title">巡检指导</span>
          <Select
            className="video-selector"
            value={videoList.length > 0 ? videoList[0].id : undefined}
            onChange={(value) => {
              const selectedVideo = videoList.find(video => video.id === value);
              if (selectedVideo) {
                setCurrentVideoUrl(selectedVideo.fileUrl);
              }
            }}
            placeholder={videoList.length > 0 ? "选择巡检指导视频" : "无视频"}
            disabled={videoList.length === 0}
            options={videoList.map((item: any) => ({ label: item.fileName, value: item.id }))}
          >
          </Select>
        </div>

        {/* 巡检指导区域 */}
        <div className="guidance-section">
          <div className="video-container">
            {videoList.length === 0 ? (
              <div className="video-placeholder">
                <PlayOutline className="play-icon" />
                <div className="placeholder-text">无视频</div>
              </div>
            ) : currentVideoUrl ? (
              <video
                controls
                width="100%"
                height="200"
                src={currentVideoUrl}
                style={{ borderRadius: '8px' }}
              >
                您的浏览器不支持视频播放
              </video>
            ) : (
              <div className="video-placeholder">
                <PlayOutline className="play-icon" />
                <div className="placeholder-text">加载中...</div>
              </div>
            )}
          </div>
        </div>

        <div className="section-header">
          <span className="section-icon requirements-icon"></span>
          <span className="section-title">检查要求</span>
          <div className="action-buttons">
            <button
              className={`check-btn ${displayMode === 'analysis' ? 'active' : ''}`}
              onClick={handleSmartAnalysis}
            >
              智能分析
            </button>
            <button
              className={`check-btn ${displayMode === 'form' ? 'active' : ''}`}
              onClick={handleOpenForm}
            >
              表单填报
            </button>
          </div>
        </div>

        {/* 检查要求区域 */}
        {displayMode !== 'form' && shouldShowPhotoUpload() && (
          <div className="requirements-section">
            {/* 智能分析 */}
            {displayMode === 'analysis' && (
              <div className="analysis-content">
                {pngList.every(item => item.value) ? (
                  <div className="requirements-list">
                    {pngList.map((req, index) => (
                      <div key={req.id} className="requirement-item">
                        <span className="requirement-number">{index + 1}.</span>
                        <span className="requirement-content">{req.content}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="analysis-sections">
                    <div className="analysis-results">
                      {pngList.map((result, index) => (
                        <div key={result.id} className="analysis-item">
                          <span className="analysis-number">{index + 1}.</span>
                          <span className="analysis-requirement">{result.content}</span>
                          <span className="analysis-result">{result.value}</span>
                          {result.imagUrl && (
                            <div className="uploaded-images">
                              {result.imagUrl.map((item, index) => (
                                <div key={index} className="image-item">
                                  <img src={item} alt={`拍摄图片${index + 1}`} />
                                  <div className="image-actions">
                                    <div className="delete-overlay" onClick={() => handleDeleteImage(index)}>
                                      <CloseOutline />
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* 显示拍摄的图片 */}
                    {/* {uploadStatus === 'photo' && (
                    <div className="uploaded-images">
                      {capturedImages.map((item, index) => (
                        <div key={index} className="image-item">
                          <img src={item.url} alt={`拍摄图片${index + 1}`} />
                          <div className="image-actions">
                            <div className="delete-overlay" onClick={() => handleDeleteImage(index)}>
                              <CloseOutline />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )} */}
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {displayMode !== 'form' && shouldShowPhotoUpload() && (<div className="upload-section">
          <div className="upload-buttons">
            <Button
              className="upload-btn photo-btn"
              block
              onClick={handleOpenPhotoCapture}
            >
              <CameraOutline /> {uploadStatus === 'photo' ? '再次拍照' : '拍照上传'}
            </Button>
          </div>
        </div>)}

        {/* 检查要求区域 */}
        {displayMode !== 'form' && shouldShowVoiceRecognition() && (
          <div className="requirements-section">
            {/* 只有在没有AI分析结果时才显示检查要求列表 */}
            {voiceAnalysisResults.length === 0 && (
              <div className="requirements-list">
                {requirements.map((req, index) => (
                  <div key={req.id} className="requirement-item">
                    <span className="requirement-number">{index + 1}.</span>
                    <span className="requirement-content">{req.content}</span>
                  </div>
                ))}
              </div>
            )}

            {/* AI分析结果区域 - 替换检查要求列表 */}
            {requirements.every(item => item.value) && (
              <div className="analysis-sections">
                <div className="section-header">
                  <span className="analysis-icon"></span>
                  <span className="section-title">分析结果</span>
                </div>
                <div className="analysis-results">
                  {requirements.map((result, index) => (
                    <div key={result.id} className="analysis-item">
                      <span className="analysis-number">{index + 1}.</span>
                      <span className="analysis-requirement">{result.content}</span>
                      <span className="analysis-result">{result.value}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 语音识别按钮 - 根据checkWay和displayMode控制显示 */}
        {displayMode !== 'form' && shouldShowVoiceRecognition() && (
          <div className="voice-section">
            <Button
              className={`voice-btn ${hasVoiceRecord ? 'completed' : ''}`}
              block
              onClick={handleVoiceRecord}
            >
              <SoundOutline />
              {hasVoiceRecord ? '重新识别' : '语音识别'}
            </Button>
          </div>
        )}

        {/* 表单填报 */}
        {displayMode === 'form' && (
          <FormSubmission
            formItems={taskData.versions[0].results}
            onSubmit={(data) => {
              onSubmit(data);
            }}
            onUpload={(itemId) => {
              console.log('上传文件:', itemId);
              // 这里可以调用拍照或选择文件的功能
              handleOpenPhotoCapture();
            }}
          />
        )}
      </div>

      {/* PhotoCapture 组件 */}
      <PhotoCapture
        visible={showPhotoCapture}
        onClose={handleClosePhotoCapture}
        onImageCaptured={handlePhotoCaptured}
        title="拍照上传"
        quality={0.8}
        maxWidth={1200}
        maxHeight={1200}
      />

      {/* 语音输入组件 */}
      {showVoiceInput && (
        <div className="voice-input-overlay">
          <SpeechRecognitionContainer
            onSend={handleVoiceSend}
            onClose={handleVoiceClose}
          />
        </div>
      )}
    </div>
  );
};

export default CheckItemDetail;
