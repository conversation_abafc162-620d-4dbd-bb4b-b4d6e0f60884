/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-22 11:51:01
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-24 22:02:41
 * @FilePath: src/tools/speech-recognition-wechat/index.js
 * @Version: 1.0.0
 * @Description: 组件描述
 */
// @ts-ignore
import { message } from 'antd';
// @ts-ignore
import wx from 'weixin-js-sdk';

let localId = '';

const configWeChat = (config) => {
	wx.config({
		debug: false,
		appId: config.appId,
		timestamp: config.timestamp,
		nonceStr: config.nonceStr,
		signature: config.signature,
		jsApiList: ['startRecord', 'stopRecord', 'translateVoice'],
		success: function (res) {
			message.success('config success');
			// alert('微信JSSDK 配置成功');
		},
	});
	wx.ready(function () {
		wx.checkJsApi({
			jsApiList: ['startRecord', 'stopRecord', 'translateVoice'],
			success: function (res) {
				// alert('微信JSSDK支持的API：' + JSON.stringify(res));
				// console.log('微信JSSDK支持的API：', res);
				// message.success('支持的API：' + JSON.stringify(res));
			},
			error: function (res) {
				// console.log('微信JSSDK支持的API：', res);
				// message.success('微信JSSDK支持的API：', res);
			},
		});

		/*wx.onVoiceRecordEnd({
			complete: function (res) {
				localId = res.localId;
				// 将语音转换为文字
				wx.translateVoice({
					localId: localId,
					success: function (res) {
						// resultDiv.textContent = '转换结果：' + res.translateResult;
					},
					fail: function (res) {
						alert('转换失败：' + JSON.stringify(res));
					}
				});
			}
		});*/
	});
};

const startRecordWeChat = () => {
	wx.startRecord({
		success: function (res) {
			message.success('wx开始录音');
			localId = res.localId;
			// wx.translateVoice({
			// 	localId: localId,
			// 	isShowProgressTips: 1,
			// 	success: function (res) {
			// 		// message.success(res.translateResult);
			// 		// alert('开始录音成功' + res.translateResult);
			// 	},
			// });
		},
		fail: function (res) {
			// alert('开始录音失败：' + JSON.stringify(res));
			message.error('开始录音失败：' + JSON.stringify(res));
		},
	});
};

const stopRecordWeChat = (cb, fail) => {
	wx.stopRecord({
		success: function (res) {
			localId = res.localId;
			message.success('停止转化');
			// 将语音转换为文字
			wx.translateVoice({
				localId: localId,
				success: function (res) {
					message.success('转换成功');
					cb && cb(res.translateResult);
				},
				fail: function (res) {
					alert('转换失败：' + JSON.stringify(res));
				},
			});
		},
		fail: function (res) {
			alert('停止录音失败：' + JSON.stringify(res));
		},
	});
};

const onVoiceRecordEnd = () => {
};

export {
	configWeChat,
	startRecordWeChat,
	stopRecordWeChat,
	onVoiceRecordEnd,
};
