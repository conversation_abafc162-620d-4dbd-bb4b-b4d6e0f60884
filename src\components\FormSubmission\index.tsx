/*
 * @Description: 表单填报组件
 * @Author: AI Assistant
 * @Date: 2025-07-08
 */
import React, { useState } from 'react';
import { But<PERSON>, Stepper, Selector, Input, ImageViewer } from 'antd-mobile';
import { Select } from 'antd';
import { CameraOutline, CloseOutline } from 'antd-mobile-icons';
import PhotoCapture from '../PhotoCapture';
import { beforeUpload } from '../../util/method';
import './index.less';

// 表单项类型定义
interface FormItem {
  id: string;
  title: string;
  type: 'SINGLE_CHOICE' | 'MULTIPLE_CHOICE' | 'TEXT';
  options?: string[];
  value?: any;
  required?: boolean;
  index?: number;
}

// 表单数据接口
interface FormData {
  [key: string]: any;
}

interface SubmissionItem {
  id: string;
  images: File[];
  value: any;
  isHazard: number;
  material: string;
}

type SubmissionData = SubmissionItem[];

interface FormSubmissionProps {
  formItems?: any[];
  onSubmit?: (data: SubmissionData) => void;
  onUpload?: (itemId: string) => void;
}

const FormSubmission: React.FC<FormSubmissionProps> = ({
  formItems = [],
  onSubmit,
  onUpload
}) => {
  const [formData, setFormData] = useState<FormData>({});
  const [showPhotoCapture, setShowPhotoCapture] = useState(false);
  const [currentItemId, setCurrentItemId] = useState<string>('');
  const [imageData, setImageData] = useState<{[key: string]: {file: File, dataUrl: string,res: string}[]}>({});
  const [statusData, setStatusData] = useState<{[key: string]: number}>({});
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [currentImages, setCurrentImages] = useState<string[]>([]);

  // 默认表单项数据（模拟接口返

  const items = formItems.map((item, index) => ({
    ...item,
    // "\"是\"；\"否\"" 去掉引号
    options: (item.config.outputContent).split('；').map(option => option.replace(/"/g, '')),
    title: item.config.reqName,
    index,

  }));
  const dropdownOptions = [
    { label: '正常', value: 0 },
    { label: '隐患', value: 1 },
  ]

  // 处理选择项变化
  const handleSelectChange = (itemId: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [itemId]: value
    }));
  };

  // 处理数量变化
  const handleNumberChange = (itemId: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [itemId]: value
    }));
  };

  // 处理状态选择变化
  const handleStatusChange = (itemId: string, value: number) => {
    setStatusData(prev => ({
      ...prev,
      [itemId]: value
    }));
  };

  // 处理上传
  const handleUpload = (itemId: string) => {
    setCurrentItemId(itemId);
    setShowPhotoCapture(true);
  };

  // 关闭拍照组件
  const handleClosePhotoCapture = () => {
    setShowPhotoCapture(false);
    setCurrentItemId('');
  };

  // 处理拍照完成
  const handlePhotoCaptured = (file: File, dataUrl: string) => {
    beforeUpload(file, (res) => {
      debugger
      setImageData(prev => ({
        ...prev,
        [currentItemId]: [...(prev[currentItemId] || []), {file, dataUrl,res: res[0].id}]
      }));
      setShowPhotoCapture(false);
      setCurrentItemId('');
    })
  };

  // 预览图片
  const handleImagePreview = (itemId: string, imageIndex: number) => {
    const images = imageData[itemId] || [];
    const imageUrls = images.map(img => img.dataUrl);
    setCurrentImages(imageUrls);
    setCurrentImageIndex(imageIndex);
    setImageViewerVisible(true);
  };

  // 删除图片
  const handleDeleteImage = (itemId: string, imageIndex: number) => {
    setImageData(prev => ({
      ...prev,
      [itemId]: (prev[itemId] || []).filter((_, index) => index !== imageIndex)
    }));
  };

  // 处理提交
  const handleSubmit = () => {
    debugger
    if (onSubmit) {
      const submissionData: SubmissionData = items.map(item => ({
        id: item.id,
        images: (imageData[item.id] || []).map(imageItem => imageItem.file),
        material: (imageData[item.id] || []).map(imageItem => imageItem.res).join(','),
        value: formData[item.id] || "",
        isHazard: statusData[item.id] || 0
      }));
      onSubmit(submissionData);
    }
  };

  // 渲染多选项目
  const renderSelectorItem = (item: any) => (
    <div key={item.id} className="form-item">
      <div className="form-item-header">
        <span className="form-item-title">{`${item.index + 1}.${item.title}`}</span>
        <div className="status-selector">
          <Select
            placeholder="请选择"
            style={{ width: 120 }}
            options={dropdownOptions?.map(option => ({ label: option.label, value: option.value }))}
            value={statusData[item.id]}
            onChange={(value) => {
              handleStatusChange(item.id, value)
            }}
          />
        </div>
      </div>
      <div className="form-item-content">
        <div className="select-buttons">
        <Selector
            columns={3}
            multiple
            options={item.options?.map(option => ({ label: option, value: option }))}
          />
        </div>
        <div className="upload-section">
          <div
            className="upload-btn"
            onClick={() => handleUpload(item.id)}
          >
            <CameraOutline />
            <span>点击上传</span>
          </div>
          {/* 图片预览区域 */}
          {imageData[item.id] && imageData[item.id].length > 0 && (
            <div className="image-preview-list">
              {imageData[item.id].map((imageItem, index) => (
                <div key={index} className="image-preview-item">
                  <img
                    src={imageItem.dataUrl}
                    alt={`上传图片${index + 1}`}
                    onClick={() => handleImagePreview(item.id, index)}
                    className="preview-image"
                  />
                  <div
                    className="delete-btn"
                    onClick={() => handleDeleteImage(item.id, index)}
                  >
                    <CloseOutline />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
  // 渲染选择项
  const renderSelectItem = (item: any) => (
    <div key={item.id} className="form-item">
      <div className="form-item-header">
        <span className="form-item-title">{`${item.index + 1}.${item.title}`}</span>
        <div className="status-selector">
          <Select
            placeholder="请选择"
            style={{ width: 120 }}
            options={dropdownOptions?.map(option => ({ label: option.label, value: option.value }))}
            value={statusData[item.id]}
            onChange={(value) => {
              handleStatusChange(item.id, value)
            }}
          />
        </div>
      </div>
      <div className="form-item-content">
        <div className="select-buttons">
          {item.options.map(option => (
            <Button
              key={option}
              className={`select-btn ${formData[item.id] === option ? 'active' : ''}`}
              onClick={() => handleSelectChange(item.id, option)}
            >
              {option}
            </Button>
          ))}
        </div>
        <div className="upload-section">
          <div
            className="upload-btn"
            onClick={() => handleUpload(item.id)}
          >
            <CameraOutline />
            <span>点击上传</span>
          </div>
          {/* 图片预览区域 */}
          {imageData[item.id] && imageData[item.id].length > 0 && (
            <div className="image-preview-list">
              {imageData[item.id].map((imageItem, index) => (
                <div key={index} className="image-preview-item">
                  <img
                    src={imageItem.dataUrl}
                    alt={`上传图片${index + 1}`}
                    onClick={() => handleImagePreview(item.id, index)}
                    className="preview-image"
                  />
                  <div
                    className="delete-btn"
                    onClick={() => handleDeleteImage(item.id, index)}
                  >
                    <CloseOutline />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // 渲染数量项
  const renderNumberItem = (item: any) => (
    <div key={item.id} className="form-item">
      <div className="form-item-header">
        <span className="form-item-title">{`${item.index + 1}.${item.title}`}</span>
        <div className="status-selector">
          <Select
            placeholder="请选择"
            style={{ width: 120 }}
            options={dropdownOptions?.map(option => ({ label: option.label, value: option.value }))}
            value={statusData[item.id]}
            onChange={(value) => {
              handleStatusChange(item.id, value)
            }}
          />
        </div>
      </div>
      <div className="form-item-content">
        <div className="number-input">
          <Input
            value={item.value}
            onChange={(value) => handleNumberChange(item.id, value)}
            className="form-stepper"
          />
        </div>
      </div>
    </div>
  );

  return (
    // TEXT、SINGLE_CHOICE、MULTIPLE_CHOICE
    <div className="form-submission">
      <div className="form-items">
        {items.map(item => {
          switch (item.config.outputType) {
            case 'SINGLE_CHOICE':
              return renderSelectItem(item);
            case 'TEXT':
              return renderNumberItem(item);
            case 'MULTIPLE_CHOICE':
              return renderSelectorItem(item);
            
            default:
              return null;
          }
        })}
      </div>
      
      <div className="form-footer">
        <Button 
          className="submit-btn"
          onClick={handleSubmit}
          block
        >
          提交
        </Button>
      </div>
      {/* PhotoCapture 组件 */}
      <PhotoCapture
        visible={showPhotoCapture}
        onClose={handleClosePhotoCapture}
        onImageCaptured={handlePhotoCaptured}
        title="拍照上传"
        quality={0.8}
        maxWidth={1200}
        maxHeight={1200}
      />

      {/* 图片查看器 */}
      <ImageViewer
        image={currentImages[currentImageIndex]}
        visible={imageViewerVisible}
        onClose={() => setImageViewerVisible(false)}
        getContainer={null}
      />
    </div>
  );
};

export default FormSubmission;
