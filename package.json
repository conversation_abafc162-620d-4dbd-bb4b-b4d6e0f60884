{"name": "security-exam-nfdw", "private": true, "version": "0.0.2", "type": "module", "releaseAddress": "../../export-package", "scripts": {"dev": "vite --mode development", "build": "tsc && vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "5.x", "@icon-park/react": "^1.4.2", "@types/node": "^20.10.0", "@types/react-router-dom": "^5.3.3", "@uidotdev/usehooks": "^2.4.1", "antd": "^5.22.4", "antd-mobile": "^5.33.0", "antd-mobile-icons": "^0.3.0", "axios": "^1.6.2", "axios-hooks": "^5.0.2", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "event-source-polyfill": "^1.0.31", "jsqr": "^1.4.0", "less": "^4.2.0", "lodash": "^4.17.21", "lodash.isequal": "^4.5.0", "markdown-it": "^14.1.0", "markdown-to-jsx": "^7.7.10", "ol": "^10.3.0", "pubsub-js": "^1.9.5", "qs": "^6.13.1", "query-string": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-infinite-scroller": "^1.2.6", "react-router-dom": "^6.20.0", "react-slick": "^0.29.0", "slick-carousel": "^1.8.1", "turf": "^3.0.14", "weixin-js-sdk": "^1.6.5", "xlsx": "^0.18.5", "zustand": "^4.4.6"}, "devDependencies": {"@types/lodash.isequal": "^4.5.8", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-slick": "^0.23.12", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react-swc": "^3.5.0", "archiver": "^7.0.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss-pxtorem": "^6.0.0", "sass-embedded": "^1.89.2", "typescript": "^5.2.2", "vite": "^5.0.0", "vite-plugin-zip-pack": "^1.2.4"}}