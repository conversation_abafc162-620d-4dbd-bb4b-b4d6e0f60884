/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-12-04 15:20:08
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2024-12-06 11:39:45
 * @FilePath: \react-h5-template\src\api\mock.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import useAxios from 'axios-hooks';
import {Page, Result} from "@/types/http";

let apiUrl = '/api';

// 如果在生产环境中且 VITE_API_URL 为空，使用当前页面 URL
if (!apiUrl && process.env.NODE_ENV === 'production') {
  apiUrl = window.location.origin; // 或者 window.location.protocol + '//' + window.location.host
}

export interface MockResult {
    id: number;
}

export interface MockPage {
    id: number;
}

/**
 * fetch the data
 * 详细使用可以查看 useAxios 的文档
 */
export const useFetchXXX = () => {
    // set the url 
    // fetch the data
    const [{data, loading, error}, refetch] = useAxios<Result<MockResult>>(url);
    // to do something
    return {data, loading, error, refetch};
}


/**
 * fetch the data with page
 * 详细使用可以查看 useAxios 的文档
 */
export const useFetchPageXXX = (page: number, size: number) => {
    // set the url
    const url = `/xxx/xxx?page=${page}&size=${size}`;
    // fetch the data
    const [{data, loading, error}, refetch] = useAxios<Page<MockPage>>(url);
    // to do something
    return {data, loading, error, refetch};
}

// 获取地图底图
// getNowUseMap: {
//     url: "data/gisMap/getNowUseMap",
//     type: "get",
//   },
 
// https://**************:19999/ecis/data/gisMap/getNowUseMap?token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIzIiwibWlkIjoyLCJvaWQiOjIsInIiOjQxLCJzbm0iOiLnjovmlowiLCJkZXYiOiJ3ZWIiLCJ1c3IiOiJ3YW5nYmluIiwidiI6MiwiY3RpIjoxLCJleHAiOjE3NjQyMDcxMzEsImRpZCI6IndlYiIsImNpZCI6MH0._B6DBRz-20RX9BuoWRZ_IBrMREutRH7lsKtxTDcDWyM&t=1733448732954

export const useGetNowUseMap = () => {
    console.log('API URL:', apiUrl);
    const url = apiUrl+"/ecis/data/gisMap/getNowUseMap?token="+sessionStorage.getItem("token")+"&t="+new Date().getTime();
    const [{data, loading, error}, refetch] = useAxios<Result<any>>(url);
    return {data, loading, error, refetch};
}

 // 获取地图遮罩所在地区的行政区域边界接口
//  getAroundPoints: { url: "data/gisMap/getAroundPoints", type: "get" },
