/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-12 11:56:43
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-23 14:45:05
 * @FilePath: src/components/speech-recognition/index.module.scss
 * @Version: 1.0.0
 * @Description: 组件描述
 */
.speech-recognition {
  position: absolute;
  display: inline-block;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  pointer-events: auto;

  &.full-screen {

    & > .bottom-wrapper {
      height: 90%;

      .input-wrapper {
        height: 12.5rem;
      }
    }
  }

  .bottom-wrapper {
    position: absolute;
    width: 100%;
    height: 19.375rem;
    bottom: 0;
    background: #F2F5F7;
    border-radius: 10px 10px 0 0;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    .close-button-wrapper {
      position: relative;
      width: 100%;
      display: flex;
      height: 1.5rem;
      align-items: center;
      justify-content: center;
      z-index: 1;

      .close-button {
        width: 5.625rem;
        height: 100%;
        background: url('./images/icon_close.svg') no-repeat center center;
        background-size: 100% 100%;
      }
    }

    .input-wrapper {
      position: relative;
      margin: 0.75rem 0.75rem 0 0.75rem;
      height: 46px;
      border-radius: 10px;
      background: #FFFFFF;
      border: 1px solid rgba(0, 0, 0, 0.06);
      box-sizing: border-box;
      z-index: 1;

      .text-area {
        position: absolute;
        padding: 12px 0 0 12px;
        left: 0;
        right: 40px;
        height: 100%;
        width: auto;
        line-height: 1.5;
        font-size: 15px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.8);
        text-align: left;
        border: none;
        caret-color: #4873FF;
        resize: none;
        white-space: pre-wrap;
        word-break: break-all;
        overflow-wrap: break-word;
        box-sizing: border-box;
        box-shadow: none;
      }

      .full-screen-btn {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        width: 1.25rem;
        height: 1.25rem;
        background: url('./images/icon_arrow_alt.svg') center center no-repeat;
      }
    }

    .action-wrapper {
      position: absolute;
      display: flex;
      bottom: 0;
      width: 100%;
      height: 100%;
      align-items: flex-end;

      .wait-wrapper {
        position: fixed;
        display: flex;
        padding-bottom: 2rem;
        bottom: 0;
        width: 100%;
        gap: 3.5rem;
        align-items: center;
        justify-content: center;

        .clear-wrapper {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.25rem;
          user-select: auto;

          .clear-button {
            width: 1.125rem;
            height: 1.125rem;
            background: url('./images/icon_clear.svg') no-repeat center center;
            background-size: 100% 100%;
          }

          .clear-text {
            color: rgba(0, 0, 0, 0.6);
            font-size: 13px;
          }
        }

        .speak-wrapper {
          position: relative;

          .speak-text {
            position: absolute;
            margin: auto;
            top: -1.25rem;
            left: 0;
            right: 0;
            color: rgba(0, 0, 0, 0.4);
            font-size: 12px;
            text-align: center;
          }

          .speak-button {
            display: flex;
            width: 80px;
            height: 80px;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: linear-gradient(315deg, rgba(35, 60, 138, 0.9) 0%, rgba(72, 115, 255, 0.9) 100%);

            & > img {
              pointer-events: none !important;
              user-select: none;
              -webkit-touch-callout: none;
              -webkit-user-select: none;
              -moz-user-select: none;
              -ms-user-select: none;
            }
          }
        }

        .send-wrapper {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.25rem;

          &.disabled {
            pointer-events: none;
          }

          &.active {
            pointer-events: auto;

            .send-button {
              background: url('./images/icon_send_active.svg') no-repeat center center;
            }

            .send-text {
              color: #4873FF;
            }
          }

          .send-button {
            width: 1.125rem;
            height: 1.125rem;
            background: url('./images/icon_send.svg') no-repeat center center;
            background-size: 100% 100%;
          }

          .send-text {
            color: rgba(0, 0, 0, 0.3);
            font-size: 13px;
          }
        }
      }

      .speech-wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        gap: 0.5rem;

        .text {
          font-weight: 400;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.4);
        }

        .animation-wrapper {
          display: flex;
          padding: 0 1.875rem;
          width: 100%;
          height: 5rem;
          background: #DDEDF7;
          border-radius: 56px 56px 0 0;
          align-items: center;

          .animation {
            width: 100%;
            height: 30px;
            background: url('./images/speech-animation.gif') no-repeat center center;
            background-size: 370px 30px;
          }
        }
      }

      .hide {
        display: none;
      }
    }
  }
}