.answer-content {
    width: 100%;
    font-size: 16px;
    line-height: 1.6;
    color: #333333;
    display: inline-block;
    padding: 1rem;

    .forbid-play {
        width: 600px;
        height: 300px;
        background-color: #000000;
        color: #FFFFFF;
        display: flex;
        justify-content: center;
        align-items: center;

        span {
            color: #4873FF;
            margin: 0 0.5rem;
            cursor: pointer;
        }
    }

    video {
        width: 100%;
        max-height: 30rem;
    }

    .plugins-loading {
        font-size: 16px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.8);
        margin: 1rem 0 0.5rem;
        display: flex;
        gap: 0.5rem;
        align-items: center;

        :global {
            .anticon {
                font-size: 14px;
                color: #4873FF;
            }
        }
    }

    .plugins-list {
        max-width: 50rem;
        margin: 0 auto;

        .plugins-list-title {
            font-size: 16px;
            font-weight: bold;
            color: rgba(0, 0, 0, 0.8);
            margin: 1rem 0 0.5rem;
            display: flex;
            gap: 1rem;
        }

        .list-container {
            .plugin-item {
                width: fit-content;
                max-width: calc(100% - 3rem);
                font-size: 16px;
                word-break: break-all;
                color: #4873FF;
                cursor: pointer;
                margin-right: 0.5rem;
                padding-left: 1rem;
                position: relative;

                &::after {
                    content: '';
                    width: 6px;
                    height: 6px;
                    border-radius: 100%;
                    background-color: #000000;
                    position: absolute;
                    top: 10px;
                    left: 0;
                }
            }
        }

        .genPaper-plugin {
            .genPaper-plugin-file {
                display: inline-block;
                color: #4873FF;
                cursor: pointer;
            }
        }

        .video-plugin {
            video {
                width: 100%;
                max-height: 30rem;
            }
            ul {
                padding-left: 1rem;
                padding-bottom: 0.5rem;
                list-style: disc;
                // li {
                //     font-weight: bold;
                // }
            }
            .video-best-empty {
                margin-bottom: 2rem;
            }
            .video-recommend {
                margin-top: 1rem;
                .video-recommend-title {
                    display: flex;
                    justify-content: space-between;
                    
                }
                .video-recommend-list {
                    margin-top: 0.5rem;
                    display: flex;
                    gap: 0.75rem;
                    .video-recommend-item {
                        max-width: 50%;
                        position: relative;
                        flex: 1;
                        .video-recommend-item-title {
                            font-size: 12px;
                            width: 100%;
                            padding: 2px 4px;
                            color: #FFF;
                            background-color: #000000;
                            z-index: 9;
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                }
                .video-recommend-btn {
                    font-size: 14px;
                    width: fit-content;
                    margin-top: 0.5rem;
                    border: 1px solid #D7D7D7;
                    color: rgba(0,0,0,0.5);
                    border-radius: 16px;
                    display: flex;
                    gap: 0.5rem;
                    align-items: center;
                    padding: 0 0.5rem;
                    img {
                        width: 16px;
                        height: 16px;
                    }
                }
            }
        }

    }

    .exam-desc {
        font-weight: bold;
    }

    .ledger-tree-item {
        display: inline-flex;
        align-items: center;
        gap: 1rem;
        img {
            width: 18px;
            height: 18px;
        }
        :global {
            .anticon {
                color: #4873FF;
                font-size: 18px;
                cursor: pointer;
            }
        }
    }
    .websearch {
        margin-top: 0.75rem;
        font-size: 14px;
        .websearch-title {
            color: #8b8b8b
        }
        .websearch-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            .websearch-list-item {
                display: flex;
                flex-direction: row;
                color: #4873FF;
                align-items: start;
                cursor: pointer;
                img {
                    width: 16px;
                    height: 16px;
                    margin: 0.2rem 0.25rem;
                }
            }
        }
    }
    .risk-example-list {
        margin: 0.25rem 0;
        display: flex;
        gap: 0.5rem;
        img {
            max-width: 10rem;
            max-height: 10rem;
        }
    }
    :global {
        .ant-tree {
            padding: 1rem 0;
            .ant-tree-treenode {
                padding-bottom: 0.75rem;
            }
            .ant-tree-iconEle {
                img {
                    width: 20px;
                    height: 20px;
                }
            }
        }
        .ant-tree-switcher-noop {
            width: fit-content;
        }
    }
    .fire-inspection {
        .fire-inspection-list {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            gap: 0.5rem;
            .list-item {
                width: calc((100% - 1.5rem) / 4);
                display: flex;
                flex-direction: column;
                .img-idx {
                    font-size: 12px;
                    text-align: center;
                }
            }
        }
    }
}

.cursor-effect {
    &::after {
        content: "";
        width: 5px;
        height: 16px;
        background-color: #000000;
        animation: cursor-blink 1s infinite;
        display: inline-block;
    }
}

@keyframes cursor-blink {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
}