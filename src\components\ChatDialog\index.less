.chat-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-dialog {
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
  position: relative;

  // 头部样式
  .chat-header {
    background: linear-gradient(135deg, #4873FF 0%, #6C5CE7 100%);
    color: white;
    padding: 20px 16px 16px;
    position: relative;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .title {
        font-size: 18px;
        font-weight: 600;
      }

      .close-btn {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        
        svg {
          font-size: 20px;
        }
      }
    }

    .progress-section {
      .progress-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        position: relative;

        .step-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;
          position: relative;

          .step-circle {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            transition: all 0.3s ease;
          }

          .step-label {
            font-size: 12px;
            text-align: center;
            opacity: 0.8;
          }

          &.active .step-circle {
            background: rgba(255, 255, 255, 0.9);
            color: #4873FF;
          }

          &.current .step-circle {
            background: #fff;
            color: #4873FF;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
          }

          // 连接线
          &:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 12px;
            left: 60%;
            right: -40%;
            height: 2px;
            background: rgba(255, 255, 255, 0.3);
            z-index: 1;
          }

          &.active:not(:last-child)::after {
            background: rgba(255, 255, 255, 0.6);
          }
        }
      }

      .progress-bar {
        margin-top: 8px;
      }
    }
  }

  // 对话内容区域
  .chat-content {
    flex: 1;
    overflow: hidden;
    background: #f8f9fa;

    .messages-container {
      height: 100%;
      overflow-y: auto;
      padding: 16px;
      
      .message-item {
        margin-bottom: 16px;

        &.user {
          .user-message {
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
            gap: 8px;

            .message-bubble {
              max-width: 70%;
              padding: 12px 16px;
              border-radius: 18px 18px 4px 18px;
              background: #4873FF;
              color: white;
              font-size: 14px;
              line-height: 1.4;
              word-break: break-word;
            }

            .avatar {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: #4873FF;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 12px;
              font-weight: 600;
              flex-shrink: 0;
            }
          }
        }

        &.assistant {
          .assistant-message {
            display: flex;
            align-items: flex-start;
            gap: 8px;

            .avatar {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 100%);
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 12px;
              font-weight: 600;
              flex-shrink: 0;
              margin-top: 4px;
            }

            .message-bubble {
              max-width: 70%;
              padding: 12px 16px;
              border-radius: 18px 18px 18px 4px;
              background: white;
              color: #333;
              font-size: 14px;
              line-height: 1.4;
              word-break: break-word;
              box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

              &.loading {
                padding: 16px;
              }
            }
          }
        }
      }

      // 打字效果
      .typing-indicator {
        display: flex;
        gap: 4px;
        align-items: center;

        span {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #999;
          animation: typing 1.4s infinite ease-in-out;

          &:nth-child(1) {
            animation-delay: 0s;
          }

          &:nth-child(2) {
            animation-delay: 0.2s;
          }

          &:nth-child(3) {
            animation-delay: 0.4s;
          }
        }
      }
    }
  }

  // 底部输入区域
  .chat-input {
    background: white;
    border-top: 1px solid #e5e5e5;
    padding: 12px 16px;
    padding-bottom: calc(12px + env(safe-area-inset-bottom));

    .input-container {
      .input-wrapper {
        display: flex;
        align-items: flex-end;
        gap: 8px;
        background: #f5f5f5;
        border-radius: 20px;
        padding: 8px 12px;

        .voice-btn {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          flex-shrink: 0;

          svg {
            font-size: 18px;
          }
        }

        .text-input {
          flex: 1;
          border: none;
          background: transparent;
          resize: none;
          font-size: 14px;
          line-height: 1.4;

          &:focus {
            outline: none;
          }
        }

        .voice-input {
          flex: 1;
          padding: 12px 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          color: #999;
          font-size: 14px;
          cursor: pointer;
          user-select: none;
          border-radius: 16px;
          transition: all 0.3s ease;

          &.recording {
            background: rgba(72, 115, 255, 0.1);
            color: #4873FF;

            span {
              animation: pulse 1s infinite;
            }
          }

          &:active {
            transform: scale(0.98);
          }

          svg {
            font-size: 16px;
          }
        }

        .send-btn {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          flex-shrink: 0;
          color: #999;
          transition: color 0.3s ease;

          &.active {
            color: #4873FF;
          }

          svg {
            font-size: 18px;
          }
        }
      }

      .char-count {
        text-align: right;
        font-size: 12px;
        color: #999;
        margin-top: 4px;
        padding-right: 4px;
      }
    }
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
