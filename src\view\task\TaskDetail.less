.task-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  // 头部样式
  .task-detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    .back-icon {
      font-size: 20px;
      color: #333;
      cursor: pointer;
    }

    .header-content {
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: center;
      position: relative;

      .page-title {
        font-size: 16px;
        font-weight: 500;
        margin: 0;
        color: #333;
      }
    }

    .patrol-btn {
      background-color: #4873FF;
      border: none;
      color: white;
      border-radius: 16px;
      font-size: 12px;
      padding: 6px 12px;
      height: 32px;
    }
  }

  // 模式切换标签
  .mode-tabs {
    display: flex;
    background-color: rgba(203, 203, 203, 1);
    padding: 0;

    .tab-item {
      flex: 1;
      height: 54px;
      line-height: 54px;
      text-align: center;
      font-size: 17px;
      
      color: #fff;
      cursor: pointer;
      position: relative;

      &.active {
        background: linear-gradient(135deg, #4873FF 0%, #5B8CFF 100%);
        color: white;
      }

      &:first-child.active {
        border-radius: 0 13px 13px 0;
      }

      &:last-child {
        background-color: rgba(203, 203, 203, 1);

        &.active {
          background: linear-gradient(135deg, #4873FF 0%, #5B8CFF 100%);
          border-radius: 13px 0 0 13px;
        }
      }
    }
  }
  .progress-info {
    display: flex;
    align-items: center;
    position: absolute;
    width: 65px;
    height: 84px;
    background: #00B578;
    left: 0px;
    top: 110px;
    z-index: 10;
    flex-direction: column;
    justify-content: center;

    .progress-text {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 14px;
      color: rgba(255,255,255,0.8);
      line-height: 21px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 9px;
    }

    .progress-percent {
      font-family: DIN Black, DIN Black;
      font-weight: 900;
      font-size: 20px;
      color: #FFFFFF;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
  // 进度条区域
  .progress-section {
    background-color: #fff;
    margin-bottom: 8px;
    position: relative;
    width: 100%;
    height: 84px;
    overflow-x: auto;
    position: absolute;
    top: 110px;
    left: 0px;
    

    // .stages-container-wrapper {
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   width: 100%;
    //   height: 100%;
    //   background-color: #fff;
    // }

    .stages-container {
      display: flex;
      align-items: flex-start;
      position: relative;
      padding: 0 5px;
      width: max-content;
      white-space: nowrap;
      margin-left: 50px;
      align-items: center;
      height: 100%;
      .stage-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        flex: 1;
        width: 86px;
        height: 100%;
        &.active {
          background-color: rgba(0, 0, 0, 0.08);
        }

        .stage-dot {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          position: relative;
          z-index: 3;

          .check-mark {
            width: 100%;
            height: 100%;
            background: url('../../assets/taskList/check-mark.png') no-repeat center center;
            background-size: 100% 100%;
          }

          .current-dot {
            width: 100%;
            height: 100%;
            background: url('../../assets/taskList/current-dot.png') no-repeat center center;
            background-size: 100% 100%;
          }

          .pending-dot {
            width: 100%;
            height: 100%;
            background: url('../../assets/taskList/pending-dot.png') no-repeat center center;
            background-size: 100% 100%;
          }
        }

        .stage-name {
          font-size: 12px;
          color: #333;
          text-align: center;
          font-weight: 400;
          line-height: 1.2;
          max-width: 70px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        // 虚线连接线 - 精确还原图片中的虚线样式
        &:not(:last-child)::before {
          content: '';
          position: absolute;
          top: 24px;
          left: calc(50%);
          width: calc(100% - 28px);
          height: 2px;
          background: url('../../assets/taskList/current-line.png') no-repeat center center;
          background-size: 200% 100%;
          width: 100%;
          height: 14px;
          z-index: 1;
        }

        // 不同状态的样式
        &.completed {

          .stage-name {
            color: #333;
          }

          // 已完成阶段后的连接线为绿色虚线
          &:not(:last-child)::before {
            // background-image: repeating-linear-gradient(
            //   to right,
            //   #52c41a 0px,
            //   #52c41a 6px,
            //   transparent 6px,
            //   transparent 12px
            // );
            background: url('../../assets/taskList/completed-line.png') no-repeat center center;
            background-size: 100% 100%;
            width: 100%;
            height: 14px;
          }
        }

        &.current {

          .stage-name {
            color: #333;
            font-weight: 500;
          }

          // 当前阶段后的连接线为灰色虚线
          &:not(:last-child)::before {
            // background-image: repeating-linear-gradient(
            //   to right,
            //   #d9d9d9 0px,
            //   #d9d9d9 6px,
            //   transparent 6px,
            //   transparent 12px
            // );
            background: url('../../assets/taskList/current-line.png') no-repeat center center;
            background-size: 230% 100%;
            width: 100%;
            height: 14px;
          }
        }

        &.pending {
          .stage-dot {
            background-color: #f5f5f5;
            border: 2px solid #d9d9d9;
          }

          .stage-name {
            color: #999;
          }

          // 未开始阶段的连接线为灰色虚线
          &:not(:last-child)::before {
            // background-image: repeating-linear-gradient(
            //   to right,
            //   #d9d9d9 0px,
            //   #d9d9d9 6px,
            //   transparent 6px,
            //   transparent 12px
            // );
            background: url('../../assets/taskList/current-line.png') no-repeat center center;
            background-size: 200% 100%;
            width: 100%;
            height: 14px;
          }
        }
      }
    }
  }

  // 检查项目列表
  .check-items-container {
    flex: 1;
    margin-top: 90px;
    padding: 0 16px 16px 16px;
    overflow-y: auto;

    .check-item {
      height: 98px;
      background: linear-gradient( 180deg, #FFFFFF 0%, #EBF3F9 100%);
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      cursor: pointer;
      transition: all 0.2s ease;
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
      }

      .item-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        position: relative;
        padding-left: 60px;

        .status-badge {
          position: absolute;
          left: -16px;
          top: -3px;
          font-size: 16px;
          padding: 4px 8px;
          border-radius: 0px 12px 12px 0px;
          margin-right: 12px;
          font-weight: 500;

          &.pending {
            background-color: #ff7875;
            color: white;
          }

          &.completed {
            background-color: #52c41a;
            color: white;
          }
        }

        .item-name {
          font-size: 17px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.8);
          font-weight: 700;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .requirements-count {
          font-size: 12px;
          color: #666;
          margin-right: 8px;
        }

        .arrow {
          font-size: 16px;
          color: #999;
        }
      }

      .item-stats {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .stats-text {
          font-size: 12px;
          color: #666;
          flex: 1;
        }

        .action-buttons {
          display: flex;
          gap: 8px;
          align-items: center;

          .voice-btn,
          .camera-btn {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            border: 1px solid #E5E5E5;
            background-color: rgba(72, 115, 255, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0px;

            .antd-mobile-icon {
              font-size: 16px;
              color: #4873FF;
            }

            &:active {
              border-color: #4873FF;
              background-color: rgba(72, 115, 255, 0.01);
            }
            span{
              width: 100%;
              height: 100%;
            }
          }

          .voice-btn .voice-btn-icon{
            background: url('../../assets/taskList/voice-btn.png') no-repeat center center;
            background-size: 50% 50%;
            width: 100%;
            height: 100%;
            display: block;
          }
          .camera-btn .camera-btn-icon{
            background: url('../../assets/taskList/camera-btn.png') no-repeat center center;
            background-size: 50% 50%;
            width: 100%;
            height: 100%;
            display: block;
          }
        }
      }
    }
  }

  // 分析结果容器样式
  .analysis-results-container {
    margin: 16px;
    padding: 16px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      .results-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }

      .clear-btn {
        font-size: 12px;
        height: 28px;
        border-color: #d9d9d9;
        color: #666;

        &:active {
          border-color: #4873FF;
          color: #4873FF;
        }
      }
    }

    .section-title {
      font-size: 14px;
      font-weight: 500;
      color: #666;
      margin: 0 0 12px 0;
    }

    .analyzing-section,
    .photo-analysis-section,
    .voice-analysis-section {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .analyzing-item {
      padding: 12px;
      background: linear-gradient(90deg, #f0f8ff 0%, #e6f3ff 50%, #f0f8ff 100%);
      background-size: 200% 100%;
      animation: shimmer 2s infinite;
      border-radius: 6px;
      border-left: 3px solid #4873FF;

      .analyzing-text {
        font-size: 13px;
        color: #4873FF;
        font-weight: 500;
      }
    }

    @keyframes shimmer {
      0% {
        background-position: -200% 0;
      }
      100% {
        background-position: 200% 0;
      }
    }

    .analysis-item {
      padding: 8px 12px;
      margin-bottom: 8px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border-left: 3px solid #4873FF;

      .analysis-text {
        font-size: 13px;
        color: #333;
        line-height: 1.4;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .captured-images-section {
      .images-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 8px;
        margin-top: 8px;

        .image-preview {
          position: relative;
          width: 80px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;
          border: 1px solid #e9ecef;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }
}
