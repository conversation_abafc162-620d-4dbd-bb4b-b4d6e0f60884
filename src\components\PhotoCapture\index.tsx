import React, { useState, useRef, useCallback } from 'react';
import './index.less';
import { PhotoCaptureProps, PhotoCaptureState } from './types';
import axios from 'axios';
import { Popup } from 'antd-mobile';

const PhotoCapture: React.FC<PhotoCaptureProps> = ({
  onImageCaptured,
  onUploadSuccess,
  onUploadError,
  quality = 0.7,
  uploadUrl,
  maxWidth = 800,
  maxHeight = 800,
  visible = false,
  onClose,
  title = '拍照上传'
}) => {
  const [state, setState] = useState<PhotoCaptureState>({
    originalImage: null,
    croppedImage: null,
    currentStep: 'select',
    uploading: false
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理文件选择
  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setState(prev => ({
          ...prev,
          originalImage: reader.result as string,
          currentStep: 'preview'
        }));
      };
      reader.readAsDataURL(file);
    }
  }, []);

  // 触发拍照
  const handleTakePhoto = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.setAttribute('capture', 'environment');
      fileInputRef.current.click();
    }
  }, []);

  // 图片压缩
  const compressImage = useCallback((dataUrl: string, targetQuality: number = quality): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const img = new Image();
      img.src = dataUrl;
      img.onload = () => {
        // 计算压缩后的尺寸
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width = width * ratio;
          height = height * ratio;
        }

        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.drawImage(img, 0, 0, width, height);
          const compressedDataUrl = canvas.toDataURL('image/jpeg', targetQuality);
          resolve(compressedDataUrl);
        }
      };
    });
  }, [quality, maxWidth, maxHeight]);

  // 将 DataURL 转换为 File 对象
  const dataURLtoFile = useCallback((dataUrl: string, filename: string): File => {
    const arr = dataUrl.split(',');
    const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  }, []);

  // 上传图片
  const uploadImage = useCallback(async (compressedDataUrl: string) => {
    if (!uploadUrl) {
      console.warn('未提供上传地址');
      return;
    }

    setState(prev => ({ ...prev, uploading: true }));

    try {
      const file = dataURLtoFile(compressedDataUrl, 'photo.jpg');
      const formData = new FormData();
      formData.append('file', file);

      const response = await axios.post(uploadUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      onUploadSuccess?.(response.data);
      onImageCaptured?.(file, compressedDataUrl);
    } catch (error) {
      console.error('上传失败:', error);
      onUploadError?.(error);
    } finally {
      setState(prev => ({ ...prev, uploading: false }));
    }
  }, [uploadUrl, dataURLtoFile, onUploadSuccess, onUploadError, onImageCaptured]);

  // 确认使用图片
  const handleConfirm = useCallback(async () => {
    if (state.originalImage) {
      const compressedImage = await compressImage(state.originalImage);
      
      if (uploadUrl) {
        await uploadImage(compressedImage);
      } else {
        // 如果没有上传地址，直接调用回调
        const file = dataURLtoFile(compressedImage, 'photo.jpg');
        onImageCaptured?.(file, compressedImage);
      }
      
      handleClose();
    }
  }, [state.originalImage, compressImage, uploadImage, uploadUrl, dataURLtoFile, onImageCaptured]);

  // 重新选择
  const handleReselect = useCallback(() => {
    setState({
      originalImage: null,
      croppedImage: null,
      currentStep: 'select',
      uploading: false
    });
  }, []);

  // 关闭组件
  const handleClose = useCallback(() => {
    handleReselect();
    onClose?.();
  }, [handleReselect, onClose]);

  if (!visible) {
    return null;
  }

  return (
    <Popup
      visible={visible}
      onClose={handleClose}
      position="bottom"
      bodyStyle={{
        borderTopLeftRadius: '12px',
        borderTopRightRadius: '12px',
        // minHeight: '60vh',
        maxHeight: '80vh',
        padding: 0
      }}
      closeOnMaskClick={true}
      destroyOnClose={true}
    >
      <div className="photo-capture-overlay">
        <div className="photo-capture-container">
          <div className="photo-capture-header">
          {/* <button className="close-btn" onClick={handleClose}>×</button> */}
          <h3 className="title">{title}</h3>
          <div className="placeholder"></div>
        </div>

        <div className="photo-capture-content">
          {state.currentStep === 'select' && (
            <div className="select-step">
              <div className="camera-placeholder">
                <div className="camera-icon">📷</div>
                <p>点击拍照或选择图片</p>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                style={{ display: 'none' }}
              />
              <div className="action-buttons">
                <button className="photo-btn primary" onClick={handleTakePhoto}>
                  📷 拍照
                </button>
                <button 
                  className="photo-btn secondary" 
                  onClick={() => {
                    if (fileInputRef.current) {
                      fileInputRef.current.removeAttribute('capture');
                      fileInputRef.current.click();
                    }
                  }}
                >
                  🖼️ 选择图片
                </button>
              </div>
            </div>
          )}

          {state.currentStep === 'preview' && state.originalImage && (
            <div className="preview-step">
              <div className="preview-container">
                <img src={state.originalImage} alt="预览图片" className="preview-image" />
              </div>
              <div className="action-buttons">
                <button className="photo-btn secondary" onClick={handleReselect}>
                  重新选择
                </button>
                <button 
                  className="photo-btn primary" 
                  onClick={handleConfirm}
                  disabled={state.uploading}
                >
                  {state.uploading ? '上传中...' : '确认使用'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
    </Popup>
  );
};

export default PhotoCapture;