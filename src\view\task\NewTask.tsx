/*
 * @Description: 新增任务页面
 * @Author: AI Assistant
 * @Date: 2025-06-26
 */
import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button, SearchBar } from 'antd-mobile';
import backArrow from '@/assets/home/<USER>';
import './NewTask.less';

// 巡检对象数据接口
interface InspectionTarget {
  id: string;
  name: string;
  distance: string;
}

const NewTask: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const taskType = searchParams.get('type');
  const [searchValue, setSearchValue] = useState('');
  const [selectedTarget, setSelectedTarget] = useState<string | null>(null);

  // 模拟巡检对象数据
  const inspectionTargets: InspectionTarget[] = [
    { id: '1', name: '杭州敬商科技有限公司', distance: '50.0m' },
    { id: '2', name: '阿里巴巴（中国）有限公司', distance: '2.3km' },
    { id: '3', name: '浙江荣盛控股集团有限公司', distance: '2.3km' },
    { id: '4', name: '浙江吉利控股集团有限公司', distance: '2.3km' },
    { id: '5', name: '浙江恒逸集团有限公司', distance: '2.3km' },
    { id: '6', name: '杭州市实业投资集团有限公司', distance: '2.3km' },
    { id: '7', name: '杭州钢铁集团有限公司', distance: '2.3km' },
  ];

  // 过滤搜索结果
  const filteredTargets = inspectionTargets.filter(target =>
    target.name.toLowerCase().includes(searchValue.toLowerCase())
  );

  const handleBack = () => {
    navigate(-1);
  };

  const handleTargetSelect = (targetId: string) => {
    setSelectedTarget(targetId);
    // 跳转到任务信息确认页面
    navigate(`/task-info?targetId=${targetId}&type=${taskType}`);
  };

  const getTaskTypeName = (type: string | null) => {
    switch (type) {
      case 'fire-alarm':
        return '防消联勤';
      case 'fire-work':
        return '动火作业';
      case 'school-inspection':
        return '学校巡检';
      default:
        return '未知类型';
    }
  };

  return (
    <div className="new-task-container">
      <header className="new-task-header">
        <img src={backArrow} alt="Back" className="header-icon" onClick={handleBack}/>
        <h1 className="page-title">选择巡检对象</h1>
      </header>

      <main className="new-task-content">
        {/* 搜索框 */}
        <div className="search-section">
          <SearchBar
            placeholder="请输入企业名称进行检索..."
            value={searchValue}
            onChange={setSearchValue}
            style={{
              '--border-radius': '8px',
              '--background': '#f5f5f5',
              '--height': '44px'
            }}
          />
        </div>

        {/* 巡检对象列表 */}
        <div className="targets-list">
          {filteredTargets.map((target) => (
            <div
              key={target.id}
              className={`target-item ${selectedTarget === target.id ? 'selected' : ''}`}
              onClick={() => handleTargetSelect(target.id)}
            >
              <div className="target-info">
                <span className="target-name">{target.name}</span>
                <span className='target-distance-icon'></span>
                <span className="target-distance">{target.distance}</span>
              </div>
            </div>
          ))}
        </div>
      </main>
    </div>
  );
};

export default NewTask;
