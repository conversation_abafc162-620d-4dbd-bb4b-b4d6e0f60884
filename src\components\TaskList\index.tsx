/*
 * @Description: 任务列表组件
 * @Author: AI Assistant
 * @Date: 2025-06-26
 */
import React, { useState, useEffect } from 'react';
import backArrow from '@/assets/home/<USER>';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from 'antd-mobile';
import { apis } from '@/api/api';
import './index.less';

// 任务类型枚举
export enum TaskType {
  FIRE_ALARM = 'fire-alarm',
  FIRE_WORK = 'fire-work', 
  SCHOOL_INSPECTION = 'school-inspection'
}

// 任务状态枚举
export enum TaskStatus {
  NOT_STARTED = 'not-started', // 未开始
  IN_PROGRESS = 'in-progress', // 进行中
  COMPLETED = 'completed' // 已结束
}

// 任务数据接口
export interface TaskItem {
  id: string;
  title: string;
  type: TaskType;
  status: TaskStatus;
  progress: number;
  completedCount: number;
  totalCount: number;
  foundIssues: number;
  createTime: string;
}

// 组件属性接口
interface TaskListProps {
  onTaskClick?: (task: TaskItem) => void;
  onNewTask?: (type: TaskType) => void;
  onBack?: () => void;
}

const TaskList: React.FC<TaskListProps> = ({ onTaskClick, onNewTask, onBack }) => {
  const navigate = useNavigate();
  const Location = useLocation();
  const taskType = Location.pathname.split('/')[Location.pathname.split('/').length - 1];
  const [selectedType, setSelectedType] = useState(taskType);
  const [taskList, setTaskList] = useState<TaskItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [dropdownVisible, setDropdownVisible] = useState(false);
  
  // 任务类型选项
  const [taskTypeOptions, setTaskTypeOptions] = useState<any[]>([]);

  useEffect(() => {
    apis.ginkgoSystem.getItemSceneByAiCheck().then((res) => {
      const taskTypeOptions = res.data.map((item: any) => {
        return {
          label: item.split(',')[1],
          value: item.split(',')[0]
        }
      });
      setTaskTypeOptions(taskTypeOptions);
    });
  }, []);

  // 获取任务列表数据
  const fetchTaskList = async (type: string) => {
    setLoading(true);
    try {
      // 这里应该调用实际的API接口
      // const response = await apis.task.getTaskList({ type });
      const response = await apis.ginkgoSystem.executeTaskList({ taskCode:type });
      // 模拟API调用延迟
      const mockTaskDatas = response.data.map((item: any) => {
        return {
          id: item.id,
          title: item.executeTaskName,
          type: item.taskCode,
          status: item.status===0?"in-progress":"completed",
          progress: item.completion, // 完成度
          completedCount: item.checkCount, // 已检查
          foundIssues: item.hiddenDangerCount, // 发现隐患
        }
      });
      // 根据类型过滤任务
      setTaskList(mockTaskDatas);
    } catch (error) {
      console.error('获取任务列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化和类型切换时获取数据
  useEffect(() => {
    fetchTaskList(selectedType);
  }, [selectedType]);


  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.header-dropdown')) {
        setDropdownVisible(false);
      }
    };

    if (dropdownVisible) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [dropdownVisible]);

  // 处理新增任务
  const handleNewTask = async () => {
    const response = await apis.ginkgoSystem.createTask({ taskCode:selectedType, executeTaskName:'巡检任务'+new Date().getTime()});
    console.log(response);
    debugger;
    navigate(`/task-detail/${response.data.id}?taskCode=${selectedType}`);
  };

  // 处理任务点击
  const handleTaskClick = (task: TaskItem) => {
    // onTaskClick?.(task);
    navigate(`/task-detail/${task.id}?taskCode=${task.type}`);

  };

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'in-progress':
        return '进行中';
      case 'completed':
        return '已结束';
      default:
        return '';
    }
  };

  // 获取状态样式类名
  const getStatusClassName = (status: string) => {
      switch (status) {
      case 'in-progress':
        return 'status-in-progress';
      case 'completed':
        return 'status-completed';
      default:
        return '';
    }
  };

  // 获取进度样式类名
  const getProgressClassName = (progress: number) => {
    // if (progress < 30) {
    //   return 'progress-low';
    // } else 
    return 'progress-high';

    // if (progress < 80) {
    //   return 'progress-medium';
    // } else {
    // }
  };

  return (
    <div className="task-list-container">
      {/* 顶部标题栏 */}
      <div className="task-list-header">
        <img src={backArrow} alt="Back" className="header-icon" onClick={() => navigate(-1)}/>
        <div className="header-dropdown">
          <div
            className="dropdown-trigger"
            onClick={() => setDropdownVisible(!dropdownVisible)}
          >
            <span className="dropdown-title">
              {taskTypeOptions.find(option => option.value === selectedType)?.label}
            </span>
            <span className={`dropdown-arrow ${dropdownVisible ? 'open' : ''}`}>▼</span>
          </div>
          {dropdownVisible && (
            <div className="dropdown-menu">
              {taskTypeOptions.map((option) => (
                <div
                  key={option.value}
                  className={`dropdown-item ${option.value === selectedType ? 'active' : ''}`}
                  onClick={() => {
                    setSelectedType(option.value);
                    setDropdownVisible(false);
                  }}
                >
                  {option.label}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 新增任务按钮 */}
      <div className="new-task-section">
        <Button
          className="new-task-btn"
          onClick={handleNewTask}
        >
          + 新增任务
        </Button>
      </div>

      {/* 任务列表 */}
      <div className="task-list-content">
        {loading ? (
          <div className="loading">加载中...</div>
        ) : taskList.length > 0 ? (
          taskList.map((task) => (
            <div
              key={task.id}
              className={`task-item ${getStatusClassName(task.status)}`}
              onClick={() => handleTaskClick(task)}
            >
              <div className="task-header">
                <div className="task-title">{task.title}</div>
                <div className={`task-status ${getStatusClassName(task.status)}`}>
                  {getStatusText(task.status)}
                </div>
              </div>
              <div className="task-progress">
                <span className="progress-text">
                  完成度：
                  <span className={`progress-percentage ${getProgressClassName(task.progress)}`}>
                    {task.progress}%
                  </span>
                  （已检查 <span className="">{task.completedCount}</span> 处，发现隐患 <span className="highlight-number">{task.foundIssues}</span> 个）
                </span>
              </div>
            </div>
          ))
        ) : (
          <div className="empty-state">暂无任务数据</div>
        )}
      </div>
    </div>
  );
};

export default TaskList;
