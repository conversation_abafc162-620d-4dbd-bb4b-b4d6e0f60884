/*
 * @Description:
 * @Author: cl
 * @Date: 2024-12-09 09:54:07
 * @LastEditTime: 2025-02-13 18:01:29
 * @LastEditors: 杨越 <EMAIL>
 */
import './index.less';
import inspectionAvatar from '@/assets/home/<USER>';
import backArrow from '@/assets/home/<USER>';
import logo from '@/assets/home/<USER>';
import { useNavigate } from 'react-router-dom';
import WeixinJSBridge from 'weixin-js-sdk';
import {urlName} from '@/util/method.ts';
import { useState, useEffect } from 'react';
import { apis } from '@/api/api';

function Index() {
    const navigate = useNavigate();
    const [taskTypeOptions, setTaskType] = useState([]);

    const backArrowClick = () => {
      // 返回上一级,跳回小程序安小应

      // 跳转到支付页面
      if (typeof WeixinJSBridge === "undefined") {
        if (document.addEventListener) {
          document.addEventListener('WeixinJSBridgeReady', ()=>{
            WeixinJSBridge.miniProgram.redirectTo({
              url: `../index`, // 小程序地址
            });
          }, false);
        }
      } else {
        WeixinJSBridge.miniProgram.redirectTo({
          url: `../index`, // 小程序地址
        });
      }
    }

    const navRouter = (router: string) => {
      navigate(`${urlName}/task-list/${router}`);
    }

    useEffect(() => {
      apis.ginkgoSystem.getItemSceneByAiCheck().then((res) => {
        const taskTypeOptions = res.data.map((item: any) => {
          return {
            label: item.split(',')[1],
            value: item.split(',')[0]
          }
        });
        setTaskType(taskTypeOptions);
      });
    }, []);


  //  // 任务类型选项
  // const taskTypeOptions = [
  //   { label: '防消联勤', value: TaskType.FIRE_ALARM },
  //   { label: '动火作业', value: TaskType.FIRE_WORK },
  //   { label: '学校巡检', value: TaskType.SCHOOL_INSPECTION }
  // ];


    return (
        <div className="inspection-container">
            <header className="inspection-header">
                <img src={backArrow} alt="Back" className="header-icon" onClick={backArrowClick}/>
                <div className="header-title">
                    <img src={logo} alt="AI-Inspection Logo" className="title-logo" />
                    <span>AI巡检</span>
                </div>
            </header>

            <main className="inspection-main">
                <div className="avatar-container">
                    <img src={inspectionAvatar} alt="Inspection Avatar" className="avatar-image" />
                </div>

                <div className="info-card">
                    <p>我是安全巡检小助手。我能结合图像识别、语音处理、自然语言理解、大数据分析等技术，完成多种场景的巡检任务，提升巡检的效率与准确性。</p>
                </div>

                <div className="selection-card">
                    <p className="selection-prompt">请选择你要巡检的类型：</p>
                    <div className="button-group">
                        {
                          taskTypeOptions.map((item) => (
                            <button className="inspection-button" onClick={() => navRouter(item.value)}>{item.label}</button>
                          ))
                        }
                        {/* <button className="inspection-button" onClick={() => navRouter('fire-alarm')}>防消联勤</button>
                        <button className="inspection-button" onClick={() => navRouter('fire-work')}>动火作业</button>
                        <button className="inspection-button" onClick={() => navRouter('school-inspection')}>学校巡检</button> */}
                    </div>
                </div>
            </main>

        </div>
    );
}

export default Index;
