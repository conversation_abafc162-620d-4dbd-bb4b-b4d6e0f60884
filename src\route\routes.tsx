/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2025-01-06 10:23:23
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2025-02-11 13:45:44
 * @FilePath: \note-exam\src\route\routes.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from "react";
import Home from "@/view/home";
import TaskDetail from "@/view/task/TaskDetail";
import TaskList from "@/components/TaskList";
import NewTask from "@/view/task/NewTask.tsx";
import TaskInfo from "@/view/task/TaskInfo.tsx";
import CheckItemDetail from "@/view/task/CheckItemDetail";
import InspectionRecord from "@/view/task/InspectionRecord";
import {urlName} from '@/util/method.ts';

export interface AppRoute {
    path: string;
    element: React.ReactNode;
    auth?: boolean;
    children?: AppRoute[];
    name?: string;
}

export const routes: AppRoute[] = [
    {path: `${urlName}/task-list/:type`, element: <TaskList/>, auth: false}, //任务列表页

    // Task related routes
    {path: `/task-detail/:id`, element: <TaskDetail/>, auth: false}, //任务详情页
    {path: `/check-item/:id`, element: <CheckItemDetail/>, auth: false}, //检查项目详情页
    {path: `/inspection-record/:id`, element: <InspectionRecord/>, auth: false}, //巡检记录页
    {path: `/new-task`, element: <NewTask/>, auth: false}, //新增任务页
    {path: `/task-info`, element: <TaskInfo/>, auth: false}, //任务信息确认页

    // {path: `${urlName}/knowledge-before`, element:<KnowledgeBefore/>, auth:false,
    //     children:[
    //         {path: 'pay-after', element:<PayAfter/>, auth:false,name: '' }, //付费后
    //         {path: 'pay-before/:id', element:<PayBefore/>, auth:false,name:'学前需知'}, //付费前
    //     ]
    // }, //知识点详情

    {path: '/', element: <Home/>, auth: false},
    {path: `${urlName}`, element: <Home/>, auth: false},
    {path: '*', element: <Home/>, auth: false}
];