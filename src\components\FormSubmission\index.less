.form-submission {
  background-color: #f5f5f5;
  padding: 16px;

  .form-items {
    margin-bottom: 20px;
  }

  .form-item {
    background-color: white;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .form-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .form-item-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .form-item-action {
        font-size: 12px;
        color: #999;
        display: flex;
        align-items: center;
        gap: 4px;

        &::after {
          content: '>';
          font-size: 12px;
          color: #ccc;
        }
      }

      .status-selector {
        width: 120px;

        .ant-select {
          .ant-select-selector {
            border-radius: 6px;
            border: 1px solid #e5e5e5;
            height: 32px;

            .ant-select-selection-item {
              font-size: 12px;
              line-height: 30px;
            }

            .ant-select-selection-placeholder {
              font-size: 12px;
              line-height: 30px;
              color: #999;
            }
          }

          &.ant-select-focused .ant-select-selector {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
          }
        }
      }
    }

    .form-item-content {
      .select-buttons {
        display: flex;
        gap: 12px;
        margin-bottom: 12px;

        .select-btn {
          flex: 1;
          height: 36px;
          border: 1px solid #e8e8e8;
          background-color: #f8f9fa;
          color: #666;
          border-radius: 4px;
          font-size: 14px;

          &.active {
            background-color: #e6f7ff;
            border-color: #91d5ff;
            color: #1890ff;
          }

          &:hover {
            border-color: #40a9ff;
          }
        }
      }

      .upload-section {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .upload-btn {
          width: 80px;
          height: 60px;
          background-color: #f0f9ff;
          border: 1px dashed #1890ff;
          border-radius: 4px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: #e6f7ff;
            border-color: #40a9ff;
          }

          &:active {
            transform: scale(0.98);
          }

          span {
            font-size: 11px;
            color: #1890ff;
            margin-top: 4px;
          }

          .antd-mobile-icon {
            font-size: 20px;
            color: #1890ff;
          }
        }

        .image-preview-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-top: 8px;

          .image-preview-item {
            position: relative;
            width: 60px;
            height: 60px;
            border-radius: 4px;
            border: 1px solid #e8e8e8;

            .preview-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
              cursor: pointer;
              transition: transform 0.2s ease;

              &:hover {
                transform: scale(1.05);
              }
            }

            .delete-btn {
              position: absolute;
              top: -4px;
              right: -4px;
              width: 16px;
              height: 16px;
              background-color: #ff4d4f;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
              transition: all 0.2s ease;

              &:hover {
                background-color: #ff7875;
                transform: scale(1.1);
              }

              .antd-mobile-icon {
                font-size: 10px;
                color: white;
              }
            }
          }
        }
      }

      .number-input {
        display: flex;
        justify-content: center;
        padding: 12px 0;

        .form-stepper {
          .adm-stepper-input {
            width: 80px;
            text-align: center;
            font-size: 16px;
          }

          .adm-stepper-button {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background-color: #fff;

            &:hover {
              border-color: #40a9ff;
              color: #40a9ff;
            }

            &:active {
              background-color: #f0f9ff;
            }
          }
        }
      }
    }
  }

  .form-footer {
    padding: 16px 0;

    .submit-btn {
      height: 44px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 22px;
      font-size: 16px;
      font-weight: 500;
      color: white;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
      }
    }
  }
}
