/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-28 15:51:24
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-23 14:20:11
 * @FilePath: src/components/mobile-question-input/index.module.scss
 * @Version: 1.0.0
 * @Description: 组件描述
 */

.mobile-question-input-container {
  margin: 1.5rem 0;
  width: 100%;
  //height: 7.75rem;
  /*background-color: #FFFFFF;
  box-shadow: 0px -2px 16px 0px rgba(0, 0, 0, 0.08);*/

  &.disabled {
    pointer-events: none;
  }

  &.default-scene {
    //height: 10rem;

    .mobile-question-input-wrapper {
      height: 5.375rem;

      .voice-button, .upload {
        top: auto;
        bottom: 0.75rem;
      }

      .upload {
        right: 45px;
      }

      :global {
        .adm-text-area {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          margin: 12px;
          width: auto;
          height: 22px;
          box-sizing: border-box;

          .adm-text-area-element {
            font-size: 15px;
          }
        }
      }
    }
  }

  &.upload-btn-active {
    //height: 15rem;
    margin-bottom: 1.875rem;

    .upload-btn-wrapper {
      display: flex;
    }
  }

  .upload-btn-wrapper {
    margin: 16px 20px 0 20px;
    display: none;
    height: 50px;
    justify-content: center;
    column-gap: 1rem;
    box-sizing: border-box;

    .action-btn-wrapper {
      margin-bottom: 10rem;
      width: 96px;

      .action-btn {
        width: 96px;
        height: 58px;
        border-radius: 6px 6px 6px 6px;
        pointer-events: none;

        &.take-pic {
          background: #F5F5F5 url('./images/icon_take_pic.svg') no-repeat center center;
        }

        &.album {
          background: #F5F5F5 url('./images/icon_album.svg') no-repeat center center;
        }

        &.file {
          background: #F5F5F5 url('./images/icon_file.svg') no-repeat center center;
        }
      }

      .action-label {
        margin-top: 4px;
        width: 100%;
        height: 17px;
        font-family: Source Han Sans;
        font-weight: 400;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.8);
        line-height: 17px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        pointer-events: none;
      }
    }
  }

  .capsule-tab-wrapper {
    width: 100%;
    height: 50px;
    pointer-events: none;

    &.hide {
      display: none;
    }

    .upload-file {
      margin: 0.75rem 0.5rem 0 0.75rem;
      pointer-events: auto;

      .file-item {
        position: relative;
        width: fit-content;
        max-width: 100%;
        max-height: 100%;
        font-size: 12px;
        display: flex;
        align-items: center;
        text-wrap: nowrap;
        gap: 0.5rem;
        border-radius: 4px;

        :global {
          .anticon {
            font-size: 16px;
          }

          .anticon-file-word {
            color: #006EEE
          }

          .anticon-file-pdf {
            color: #F33C33
          }

          .anticon-close-circle {
            font-size: 12px;
          }

          .ant-image {
            max-width: 50px;
            max-height: 50px;

            img {
              max-height: 50px;
            }
          }
        }

        .file-icon {
          width: 2.25rem;
          height: 2.25rem;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .file-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;

          .file-name {
            font-size: 12px;
            color: #26244c;
            white-space: normal;
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .file-type-size {
            margin-top: 0.25rem;
            display: flex;
            gap: 0.5rem;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.6);
          }
        }

        .file-del {
          position: absolute;
          top: -0.5rem;
          right: -0.5rem;
          cursor: pointer;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .img-del {
          position: absolute;
          top: -0.5rem;
          right: -0.5rem;
          cursor: pointer;

          img {
            width: 16px;
            height: 16px;
          }

        }

        video {
          width: 50px;
          height: 50px;
        }
      }
    }
  }

  .mobile-question-input-wrapper {
    position: relative;
    margin: 0 12px;
    height: 3.125rem;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 10px 10px 10px 10px;
    box-shadow: 1px 1px 6px 0 rgba(0, 18, 59, 0.12);

    .voice-button {
      position: absolute;
      width: 1.375rem;
      height: 1.625rem;
      top: 0;
      bottom: 0;
      left: 0.75rem;
      margin: auto;
      background: url('./images/icon_voice.svg') no-repeat center center;
      background-size: 100% 100%;
      box-sizing: border-box;

      &.voice-active {
        background: url('./images/icon_text.svg') no-repeat center center;
      }
    }

    .voice-label {
      position: absolute;
      display: flex;
      top: 0;
      bottom: 0;
      left: 40px;
      right: 40px;
      margin: auto;
      width: auto;
      height: 22px;
      font-family: Source Han Sans;
      font-weight: 700;
      font-size: 15px;
      color: rgba(0, 0, 0, 0.8);
      align-items: center;
      justify-content: center;

      &.active {
        top: -30px;
      }
    }

    :global {
      .adm-text-area {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 40px;
        right: 40px;
        margin: auto;
        width: auto;
        height: 22px;

        .adm-text-area-element {
          font-size: 15px;
        }
      }
    }

    .upload {
      position: absolute;
      width: 1.375rem;
      height: 1.625rem;
      top: 0;
      bottom: 0;
      right: 0.75rem;
      margin: auto;
      background: url('./images/icon_upload.svg') no-repeat center center;
      background-size: 100% 100%;

      &.upload-active {
        background: url('./images/icon_close.svg') no-repeat center center;
      }
    }

    .send {
      position: absolute;
      display: none;
      width: 1.375rem;
      height: 1.625rem;
      right: 12px;
      bottom: 0.75rem;
      align-items: center;
      background: url('./images/icon_send_disabled.svg') no-repeat center center;
      background-size: 100% 100%;

      &.show {
        display: block;
      }

      &.active {
        background: url('./images/icon_send_active.svg') no-repeat center center;
      }
    }

    .mobile-question-input-focus {
      display: none;

      &.show {
        position: absolute;
        display: flex;
        top: auto;
        bottom: 0.75rem;
        left: 45px;

        .btn-item-wrapper {
          display: flex;
          margin-right: 0.5rem;
          width: 78px;
          height: 26px;
          background: #FFFFFF;
          border-radius: 13px 13px 13px 13px;
          border: 1px solid rgba(0, 0, 0, 0.08);
          align-items: center;
          justify-content: center;
          column-gap: 3px;

          &.selected {
            background: rgba(72, 115, 255, 0.16);
            border-radius: 13px 13px 13px 13px;
            border: none;

            & > .icon {
              &.deep-thinking {
                background: url('./images/icon_deep_thinking_sel.svg') no-repeat center center;
              }

              &.online-search {
                background: url('./images/icon_online_search_sel.svg') no-repeat center center;
              }
            }

            & > .label {
              color: #4873FF;
            }
          }

          .icon {
            width: 1rem;
            height: 1rem;
            pointer-events: none;

            &.deep-thinking {
              background: url('./images/icon_deep_thinking.svg') no-repeat center center;
              background-size: contain;
            }

            &.online-search {
              background: url('./images/icon_online_search.svg') no-repeat center center;
              background-size: contain;
            }
          }

          .label {
            width: 44px;
            height: 16px;
            font-family: Source Han Sans;
            font-weight: 400;
            font-size: 11px;
            color: rgba(0, 0, 0, 0.8);
            line-height: 16px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            pointer-events: none;
          }
        }
      }
    }
  }
}