import React, { useState, useRef, useEffect } from 'react';
import { Button, TextArea, Progress, Toast } from 'antd-mobile';
import { SendOutline, VoiceOutline, CloseOutline, SoundOutline } from 'antd-mobile-icons';
import MarkdownContent from '../markdown-content';
import './index.less';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

interface ChatDialogProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  progress?: number;
  progressSteps?: string[];
  currentStep?: number;
  messages?: ChatMessage[];
  onSendMessage?: (message: string) => void;
  onVoiceMessage?: (audioBlob: Blob) => void;
  loading?: boolean;
  placeholder?: string;
  maxLength?: number;
  showVoiceButton?: boolean;
  showProgress?: boolean;
}

const ChatDialog: React.FC<ChatDialogProps> = ({
  visible,
  onClose,
  title = "请简单描述",
  progress = 0,
  progressSteps = ["请简单描述", "对话模式", "建议分析", "建议优化"],
  currentStep = 1,
  messages = [],
  onSendMessage,
  onVoiceMessage,
  loading = false,
  placeholder = "你好，请问有什么可以帮助您的吗？",
  maxLength = 500,
  showVoiceButton = true,
  showProgress = true
}) => {
  const [inputValue, setInputValue] = useState('');
  const [isVoiceMode, setIsVoiceMode] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 发送消息
  const handleSend = () => {
    if (inputValue.trim() && onSendMessage) {
      onSendMessage(inputValue.trim());
      setInputValue('');
    }
  };

  // 切换语音模式
  const toggleVoiceMode = () => {
    setIsVoiceMode(!isVoiceMode);
    if (isRecording) {
      stopRecording();
    }
  };

  // 开始录音
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        if (onVoiceMessage) {
          onVoiceMessage(audioBlob);
        }
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      Toast.show('无法访问麦克风，请检查权限设置');
      console.error('Error accessing microphone:', error);
    }
  };

  // 停止录音
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  // 处理按键事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  if (!visible) return null;

  return (
    <div className="chat-dialog-overlay">
      <div className="chat-dialog">
        {/* 头部 */}
        <div className="chat-header">
          <div className="header-content">
            <div className="title">{title}</div>
            <div className="close-btn" onClick={onClose}>
              <CloseOutline />
            </div>
          </div>
          
          {/* 进度条 */}
          {showProgress && (
            <div className="progress-section">
              <div className="progress-steps">
                {progressSteps.map((step, index) => (
                  <div
                    key={index}
                    className={`step-item ${index <= currentStep ? 'active' : ''} ${index === currentStep ? 'current' : ''}`}
                  >
                    <div className="step-circle">
                      {index < currentStep ? '✓' : index + 1}
                    </div>
                    <div className="step-label">{step}</div>
                  </div>
                ))}
              </div>
              <div className="progress-bar">
                <Progress
                  percent={progress}
                  strokeColor="#4873FF"
                  trailColor="#E5E5E5"
                  strokeWidth={4}
                />
              </div>
            </div>
          )}
        </div>

        {/* 对话内容区域 */}
        <div className="chat-content">
          <div className="messages-container">
            {messages.map((message) => (
              <div key={message.id} className={`message-item ${message.type}`}>
                {message.type === 'user' ? (
                  <div className="user-message">
                    <div className="message-bubble user-bubble">
                      {message.content}
                    </div>
                    <div className="avatar user-avatar">
                      <span>用</span>
                    </div>
                  </div>
                ) : (
                  <div className="assistant-message">
                    <div className="avatar assistant-avatar">
                      <span>AI</span>
                    </div>
                    <div className="message-bubble assistant-bubble">
                      <MarkdownContent content={message.content} />
                    </div>
                  </div>
                )}
              </div>
            ))}
            
            {/* 加载状态 */}
            {loading && (
              <div className="message-item assistant">
                <div className="assistant-message">
                  <div className="avatar assistant-avatar">
                    <span>AI</span>
                  </div>
                  <div className="message-bubble assistant-bubble loading">
                    <div className="typing-indicator">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* 底部输入区域 */}
        <div className="chat-input">
          <div className="input-container">
            <div className="input-wrapper">
              {showVoiceButton && (
                <div className="voice-btn" onClick={toggleVoiceMode}>
                  <VoiceOutline color={isVoiceMode ? "#4873FF" : "#999"} />
                </div>
              )}

              {isVoiceMode ? (
                <div
                  className={`voice-input ${isRecording ? 'recording' : ''}`}
                  onTouchStart={startRecording}
                  onTouchEnd={stopRecording}
                  onMouseDown={startRecording}
                  onMouseUp={stopRecording}
                >
                  <SoundOutline color={isRecording ? "#4873FF" : "#999"} />
                  <span>{isRecording ? '正在录音...' : '按住说话'}</span>
                </div>
              ) : (
                <TextArea
                  value={inputValue}
                  onChange={setInputValue}
                  onKeyPress={handleKeyPress}
                  placeholder={placeholder}
                  autoSize={{ minRows: 1, maxRows: 3 }}
                  className="text-input"
                  maxLength={maxLength}
                />
              )}

              {!isVoiceMode && (
                <div
                  className={`send-btn ${inputValue.trim() ? 'active' : ''}`}
                  onClick={handleSend}
                >
                  <SendOutline />
                </div>
              )}
            </div>

            {!isVoiceMode && maxLength && (
              <div className="char-count">
                {inputValue.length}/{maxLength}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatDialog;
