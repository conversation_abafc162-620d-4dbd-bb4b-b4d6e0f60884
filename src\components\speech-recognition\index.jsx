/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-12 11:56:31
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-24 21:34:25
 * @FilePath: src/components/speech-recognition/index.jsx
 * @Version: 1.0.0
 * @Description: 组件描述
 */
import { Input, message } from 'antd';
import classNames from 'classnames';
import { useEffect, useRef, useState } from 'react';
import speakIcon from './images/icon_speak.svg';
import styles from './index.module.scss';

const { TextArea } = Input;

const STATUS = {
	NORMAL: 'normal',   // 默认状态，当前文本框内为空
	SPEECH: 'speech',   // 语音状态，当前正在语音讲话中
	PAUSED: 'paused',   // 文本框内有已经输入的内容。
};

const getBrowserInterfaceSize = () => {
	let pageWidth = window.innerWidth;
	let pageHeight = window.innerHeight;

	if (typeof pageWidth !== 'number') {
		//在标准模式下面
		if (document.compatMode === 'CSS1Compat') {
			pageWidth = document.documentElement.clientWidth;
			pageHeight = document.documentElement.clientHeight;
		} else {
			pageWidth = document.body.clientWidth;
			pageHeight = window.body.clientHeight;
		}
	}

	return {
		pageWidth: pageWidth,
		pageHeight: pageHeight,
	};
};

const SpeechRecognition = ({
	                           speechText,
	                           currentStatus,
	                           onTextChange,
	                           onSpeech,
	                           onCancelSpeech,
	                           onClear,
	                           onSend,
	                           onClose,
                           }) => {
	const [textValue, setTextValue] = useState('按住说话');
	const [fullScreen, setFullScreen] = useState(false);
	const speakBtnRef = useRef(null);
	const textAreaRef = useRef(null);

	const { pageWidth, pageHeight } = getBrowserInterfaceSize();

	useEffect(() => {
		const contextmenuHandler = (e) => {
			e.preventDefault();
		};

		document.addEventListener('contextmenu', contextmenuHandler);

		let timeout;
		let startTouchClient = [0, 0];
		const speakTouchStartHandler = (e) => {
			message.success('speakTouchStartHandler111');
			const touch = e.touches[0];
			startTouchClient = [touch.clientX, touch.clientY];
			// 如果是长按，则阻止默认行为
			const touchDuration = 500; // 定义长按时间 (单位: 毫秒)
			timeout = setTimeout(() => {
				e.preventDefault(); // 阻止默认行为
				onSpeech();
			}, touchDuration);
		};

		const speakTouchEndHandler = (e) => {
			message.success('speakTouchEndHandler');
			const touch = e.changedTouches[0];
			if (startTouchClient[1] > touch.clientY + 80) {
				setTextValue('按住继续说话');
			}
			onCancelSpeech();
			clearTimeout(timeout); // 清除定时器
		};

		speakBtnRef.current.addEventListener('touchstart', speakTouchStartHandler, { passive: false });
		speakBtnRef.current.addEventListener('touchend', speakTouchEndHandler, { passive: false });
		/*document.addEventListener('touchcancel', speakTouchEndHandler, { passive: false });
		document.addEventListener('mouseup', speakTouchEndHandler, { passive: false });
		speakBtnRef.current.addEventListener('mouseleave', speakTouchEndHandler, { passive: false });*/

		return () => {
			document.removeEventListener('contextmenu', contextmenuHandler);
		};
	}, []);

	useEffect(() => {
		if (currentStatus === STATUS.NORMAL) {
			setTextValue('按住说话');
		} else if (currentStatus === STATUS.PAUSED) {
			setTextValue('按住继续说话');
		}
	}, [currentStatus]);

	useEffect(() => {
		if (textAreaRef.current && textAreaRef.current.resizableTextArea) {
			const textareaElement = textAreaRef.current.resizableTextArea.textArea;
			if (textareaElement) {
				textareaElement.scrollTop = textareaElement.scrollHeight;
			}
		}
	}, [speechText]);

	const onTextAreaChangeHandler = (e) => {
		onTextChange(e);
	};

	return (
		<div
			className={classNames(styles['speech-recognition'], {
				[styles['full-screen']]: fullScreen,
			})}
		>
			<div
				className={styles['bottom-wrapper']}
			>
				<div className={styles['close-button-wrapper']}>
					<div
						className={styles['close-button']}
						onClick={onClose}
					/>
				</div>
				<div className={styles['input-wrapper']}>
					<TextArea
						ref={textAreaRef}
						rootClassName={styles['text-area']}
						autoSize={false}
						value={speechText}
						onChange={onTextAreaChangeHandler}
						placeholder={'请说话，我在听...'}
					/>
					<div
						className={styles['full-screen-btn']}
						onClick={() => {
							setFullScreen(origin => !origin);
						}}>
					</div>
				</div>
				<div
					className={styles['action-wrapper']}
				>
					<div
						className={classNames(styles['wait-wrapper'], {
							[styles['hide']]: currentStatus === STATUS.SPEECH,
						})}
					>
						<div
							className={styles['clear-wrapper']}
							onClick={onClear}
						>
							<div className={styles['clear-button']}/>
							<div className={styles['clear-text']}>
								清空
							</div>
						</div>
						<div
							className={styles['speak-wrapper']}
						>
							<div
								className={styles['speak-text']}
							>
								{textValue}
							</div>
							<div
								className={styles['speak-button']}
								ref={speakBtnRef}
							>
								<img src={speakIcon} alt={''}/>
							</div>
						</div>
						<div
							className={classNames(styles['send-wrapper'], {
								[styles['disabled']]: speechText.length === 0,
								[styles['active']]: speechText.length > 0,
							})}
							onClick={onSend}
						>
							<div className={styles['send-button']}/>
							<div
								className={styles['send-text']}
							>
								发送
							</div>
						</div>
					</div>
					<div
						className={classNames(styles['speech-wrapper'], {
							[styles['hide']]: currentStatus !== STATUS.SPEECH,
						})}
					>
						<div
							className={styles['text']}
						>
							松手编辑，上滑取消
						</div>
						<div className={styles['animation-wrapper']}>
							<div className={styles['animation']}/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default SpeechRecognition;