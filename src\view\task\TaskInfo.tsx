/*
 * @Description: 确认任务信息页面
 * @Author: AI Assistant
 * @Date: 2025-06-26
 */
import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from 'antd-mobile';
import backArrow from '@/assets/home/<USER>';
import './TaskInfo.less';

// 任务信息接口
interface TaskInfoData {
  companyName: string;
  address: string;
  safetyOfficer: string;
  safetyPhone: string;
  buildingType: string;
  useType: string;
  taskName: string;
  inspector: string;
  teamLeader: string;
}

const TaskInfo: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const targetId = searchParams.get('targetId');
  const taskType = searchParams.get('type');

  // 根据targetId获取对应的企业信息
  const getCompanyInfo = (id: string | null) => {
    const companies = {
      '1': {
        companyName: '杭州敬商科技有限公司',
        address: '杭州市XX区XX街道XXXX1001号',
        safetyOfficer: '倪仰',
        safetyPhone: '13856564854',
        buildingType: '地上五层',
        useType: '经营',
        taskName: '敬商科技巡检督查20250619',
        inspector: '张三',
        teamLeader: '李四'
      },
      '2': {
        companyName: '阿里巴巴（中国）有限公司',
        address: '杭州市余杭区文一西路969号',
        safetyOfficer: '王明',
        safetyPhone: '13812345678',
        buildingType: '地上二十层',
        useType: '办公',
        taskName: '阿里巴巴巡检督查20250619',
        inspector: '李华',
        teamLeader: '赵六'
      }
    };
    return companies[id as keyof typeof companies] || companies['1'];
  };

  // 模拟任务信息数据
  const [taskInfo] = useState<TaskInfoData>(getCompanyInfo(targetId));

  const handleBack = () => {
    navigate(-1);
  };

  const handleStartInspection = () => {
    // 开始巡检逻辑
    console.log('开始巡检任务', {
      targetId,
      taskType,
      companyName: taskInfo.companyName
    });
    // 这里可以跳转到巡检执行页面
    // navigate('/inspection-execution');
  };

  const getTaskTypeName = (type: string | null) => {
    switch (type) {
      case 'fire-alarm':
        return '防消联勤';
      case 'fire-work':
        return '动火作业';
      case 'school-inspection':
        return '学校巡检';
      default:
        return '防消联勤';
    }
  };

  return (
    <div className="task-info-container">
      <header className="task-info-header">
        <img src={backArrow} alt="Back" className="header-icon" onClick={handleBack}/>
        <h1 className="page-title">确认任务信息</h1>
      </header>
      <div className="card-header">
        <span className="building-icon"></span>
        <span className="company-name">{taskInfo.companyName}</span>
      </div>

      <main className="task-info-content">
        {/* 基本信息 */}
        <div className="info-card">
          <div className="info-item">
            <span className="label">地址信息</span>
            <span className="value">{taskInfo.address}</span>
          </div>
          <div className="info-item">
            <span className="label">消防安全负责人</span>
            <span className="value">{taskInfo.safetyOfficer}</span>
          </div>
          <div className="info-item">
            <span className="label">消防安全负责人电话</span>
            <span className="value">{taskInfo.safetyPhone}</span>
          </div>
          <div className="info-item">
            <span className="label">建筑类型</span>
            <span className="value">{taskInfo.buildingType}</span>
          </div>
          <div className="info-item">
            <span className="label">使用性质</span>
            <span className="value">{taskInfo.useType}</span>
          </div>
        </div>


        <div className="card-header">
            <span className="task-icon"></span>
            <span className="section-title">任务信息</span>
        </div>
        {/* 任务信息 */}
        <div className="info-card task-card">
          <div className="info-item">
            <span className="label">任务名称</span>
            <span className="value">{taskInfo.taskName}</span>
          </div>
          <div className="info-item">
            <span className="label">单位场所陪同人员</span>
            <span className="value">{taskInfo.inspector}</span>
          </div>
          <div className="info-item">
            <span className="label">大队防火监督员</span>
            <span className="value">{taskInfo.teamLeader}</span>
          </div>
        </div>

        {/* 开始巡检按钮 */}
        <div className="action-section">
          <Button
            className="start-inspection-btn"
            onClick={handleStartInspection}
            block
          >
            开始巡检
          </Button>
        </div>
      </main>
    </div>
  );
};

export default TaskInfo;
