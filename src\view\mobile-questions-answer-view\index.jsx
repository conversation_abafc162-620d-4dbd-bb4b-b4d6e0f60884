/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-05 10:57:54
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-16 17:51:00
 * @FilePath: src/views/mobile-questions-answer-view/index.jsx
 * @Version: 1.0.0
 * @Description: 组件描述 文本输入和语音输入相互切换组件
 */
import MobileQuestionInput from '@/components/mobile-question-input';
import { SpeechRecognitionContainer } from '@/containers';
import { StopOutlined, SyncOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useState } from 'react';

const MobileQuestionsAnswerView = ({
		sceneType,
		showBtn,
		showStop,
		loading,
		disabled,
		onSend,
		onStopQuestion,
		onReSendQuestion,
		setSearchState,
	}) => {
	const [voiceShow, setVoiceShow] = useState(false);
	const [questionsData, setQuestionsData] = useState({});

	const onUpdateHandler = (data) => {
		setQuestionsData(data);
		setSearchState && setSearchState({
			isOpenThinking: data.deepThinkingSelected ? 1 : 0,
			isOpenWebSearch: data.onlineSearchSelected ? 1 : 0,
		});
	};

	const onSendHandler = () => {
		onSend({
			...questionsData,
			sceneType
		});
	};

	return (
		<div
			className={'mobile-questions-answer-view'}
		>
			<div
				className={'text-input-wrapper'}
			>
				{
					showBtn && (
						<div
							className={'regenerate-btn'}
						>
							{showStop && <Button icon={<StopOutlined/>} onClick={onStopQuestion}>停止生成</Button>}
							{!loading && <Button icon={<SyncOutlined/>} onClick={onReSendQuestion}>重新生成</Button>}
						</div>
					)
				}
				<MobileQuestionInput
					sceneType={sceneType}
					showStop={showStop}
					onUpdateData={onUpdateHandler}
					onSend={onSendHandler}
					onChange={() => {
						setVoiceShow(true);
					}}
				/>
			</div>
			<div
				className={'speech-wrapper'}
			>
				{
					voiceShow && (
						<SpeechRecognitionContainer
							onSend={(value) => {
								onSend({
									...questionsData,
									questions: value,
									sceneType
								});
							}}
							onClose={() => {
								setVoiceShow(false);
							}}
						/>
					)
				}
			</div>
		</div>
	);
};

export default MobileQuestionsAnswerView;