/*
 * @Description: 任务详情页面 - 巡检执行界面
 * @Author: AI Assistant
 * @Date: 2025-06-26
 */
import React, { useState,useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from 'antd-mobile';
import { LeftOutline, SoundOutline, CameraOutline } from 'antd-mobile-icons';
import './TaskDetail.less';
import PhotoCapture from '@/components/PhotoCapture';
import { SpeechRecognitionContainer } from '@/containers';
import ChatDialog from '@/components/ChatDialog';
import { getUrlParameter } from '@/util/index';
import { apis } from '@/api/api';
import { Toast } from 'antd-mobile';
import { getReqId, createEventSourceHandler, beforeUpload } from '@/util/method';
// 检查项目接口
interface CheckItem {
  id: string;
  name: string;
  requirements: number;
  checked: number;
  issues: number;
  status: 'pending' | 'completed';
}

// 巡检阶段接口
interface InspectionStage {
  id: string;
  name: string;
  status: 'pending' | 'current' | 'completed';
}

// 对话消息接口
interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

const TaskDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [taskData, setTaskData] = useState<any>({});
  // 巡检阶段数据
  const [stages,setStages] = useState<InspectionStage[]>([
    { id: '1', name: '消控室', status: 'completed' },
    { id: '2', name: '消防水源', status: 'current' },
    { id: '3', name: '建筑外立面', status: 'pending' },
    { id: '4', name: '建筑外', status: 'pending' },
    { id: '5', name: '建筑外', status: 'pending' },
    { id: '6', name: '建筑外', status: 'pending' },
    { id: '7', name: '建筑外', status: 'pending' },
    { id: '8', name: '建筑外', status: 'pending' },
    { id: '9', name: '建筑外', status: 'pending' },
    { id: '10', name: '建筑外', status: 'pending' },
    { id: '11', name: '建筑外', status: 'pending' },
    { id: '12', name: '建筑外', status: 'pending' },
    { id: '13', name: '建筑外', status: 'pending' },
  ]);

  const [currentStage, setCurrentStage] = useState<InspectionStage | null>(null);

  // 当前模式
  const [currentMode, setCurrentMode] = useState<'fill' | 'chat'>('fill');

  // 对话模式相关状态
  const [showChatDialog, setShowChatDialog] = useState(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatLoading, setChatLoading] = useState(false);
  const [chatProgress, setChatProgress] = useState(25);
  const [chatCurrentStep, setChatCurrentStep] = useState(0);

  // 检查项目数据
  const [checkItems, setCheckItems] = useState<any[]>([
    {
      id: '1',
      name: '水泵接合器',
      requirements: 4,
      checked: 0,
      issues: 0,
      status: 0
    },
    {
      id: '2',
      name: '室内消火栓',
      requirements: 1,
      checked: 0,
      issues: 0,
      status: 'pending'
    },
    {
      id: '3',
      name: '淋喷系统',
      requirements: 3,
      checked: 0,
      issues: 0,
      status: 'pending'
    },
    {
      id: '4',
      name: '室外消火栓',
      requirements: 5,
      checked: 8,
      issues: 3,
      status: 'completed'
    },
    {
      id: '5',
      name: '水泵',
      requirements: 2,
      checked: 3,
      issues: 0,
      status: 'completed'
    },
    {
      id: '6',
      name: '水泵',
      requirements: 2,
      checked: 3,
      issues: 0,
      status: 'completed'
    },
    {
      id: '7',
      name: '水泵',
      requirements: 2,
      checked: 3,
      issues: 0,
      status: 'completed'
    },
    {
      id: '8',
      name: '水泵',
      requirements: 2,
      checked: 3,
      issues: 0,
      status: 'completed'
    },
    
  ]);

  // 语音输入相关状态
  const [showVoiceInput, setShowVoiceInput] = useState(false);
  const [voiceAnalysisResults, setVoiceAnalysisResults] = useState<string[]>([]);

  // 拍照相关状态
  const [showPhotoCapture, setShowPhotoCapture] = useState(false);
  const [capturedImages, setCapturedImages] = useState<Array<{url: string, file: File}>>([]);
  const [analysisResults, setAnalysisResults] = useState<string[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [clickItem, setClickItem] = useState<any>(null);

  const handleClosePhotoCapture = () => {
    setShowPhotoCapture(false);
  };

  // 处理拍照成功
  const handlePhotoCaptured = (file: File, dataUrl: string) => {
    console.log('拍照结果:', file, dataUrl);

    // 添加到图片列表
    const newImage = { url: dataUrl, file };
    setCapturedImages(prev => [...prev, newImage]);

    // 关闭拍照界面
    setShowPhotoCapture(false);

    // 开始分析
    setIsAnalyzing(true);
    Toast.show('正在分析图片...');

    // 模拟AI分析
    beforeUpload(file, (res: any) => {
      console.log('res', res);
      const ids = res.map((item: any) => item.id).join(',');

      // 更新上传状态
      const origin = window.location.origin;
      // 生成reqId 时间戳+随机4位数字
      const reqId = getReqId();
      const params = {
        type: 0,
        query: clickItem.versions[0].id,
        executionId: JSON.parse(sessionStorage.getItem('prameData')).newId
      }
      let url = `${origin}/ginkgo/chat-api/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${clickItem.versions[0].results[0].businessId}&imageIds=${encodeURIComponent(ids)}&params=${encodeURIComponent(JSON.stringify(params))}&scene=ai_check`;
      console.log('url', url);
      // 更新语音分析结果
      const eventSourceHandler = createEventSourceHandler({
        url,
        qaList: [],
        onMessage: (qaList: any[], answer: string) => {
          console.log('qaList', qaList);
          console.log('answer', answer);
        },
        onComplete: (qaList: any[], answerId: string, questionId: string) => {
          console.log('qaList', qaList);
          console.log('answerId', answerId);
          console.log('questionId', questionId);
          Toast.show('图片识别完成');
          updateTaskData(false);
        },
        onError: (error: any) => {
          console.log('error', error);
        }
      })
      eventSourceHandler.start();
    });
  };

  // 处理语音输入发送
  const handleVoiceSend = (speechText: string) => {
    console.log('语音识别文本:', speechText);

    // 关闭语音输入界面
    setShowVoiceInput(false);

    if (!speechText.trim()) {
      Toast.show('请说话后再发送');
      return;
    }

    // 开始分析
    setIsAnalyzing(true);
    Toast.show('正在分析语音内容...');
    // 如果有当前阶段和任务数据，进行AI分析
    if (currentStage && clickItem) {
      const origin = window.location.origin;
      const reqId = getReqId();

      
      // 构建分析URL - 使用当前阶段的第一个检查项目的businessId
      const firstCheckItem = clickItem;
      if (firstCheckItem && firstCheckItem.versions[0].results[0].businessId) {
        const params = {
          type: 1,
          query: firstCheckItem.versions[0].id,
          executionId: JSON.parse(sessionStorage.getItem('prameData')).newId
        }
        const url = `${origin}/ginkgo/chat-api/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${firstCheckItem.versions[0].results[0].businessId}&question=${encodeURIComponent(speechText)}&params=${encodeURIComponent(JSON.stringify(params))}&scene=ai_check`;

        // 使用EventSource处理实时响应
        const eventSourceHandler = createEventSourceHandler({
          url,
          qaList: [],
          onMessage: (_list: any[], answer: string) => {
            // 处理接收到的数据
            if (answer && answer.trim()) {
              setVoiceAnalysisResults(prev => [...prev, answer]);
            }
          },
          onComplete: () => {
            // 完成回调
            updateTaskData(false);
            // setIsAnalyzing(false);
            // Toast.show({
            //   content: '语音分析完成',
            //   icon: 'success'
            // });
          },
          onError: (error: any) => {
            // 错误回调
            console.error('语音分析错误:', error);
            Toast.show('语音分析失败，请重试');
          }
        });

        // 启动EventSource
        eventSourceHandler.start();
      } else {
        // 如果没有检查项目数据，显示模拟分析结果
        setTimeout(() => {
          const mockAnalysis = [
            `语音内容："${speechText}"`,
            '分析结果：根据描述，建议重点检查相关设备状态',
            '请配合拍照记录现场情况'
          ];
          setVoiceAnalysisResults(prev => [...prev, ...mockAnalysis]);
          setIsAnalyzing(false);
          Toast.show({
            content: '语音分析完成',
            icon: 'success'
          });
        }, 1500);
      }
    } else {
      // 没有任务数据时的处理
      setTimeout(() => {
        const mockAnalysis = [
          `语音内容："${speechText}"`,
          '请先选择检查阶段后再进行语音分析'
        ];
        setVoiceAnalysisResults(prev => [...prev, ...mockAnalysis]);
        setIsAnalyzing(false);
      }, 1000);
    }
  };

  const handleVoiceClose = () => {
    setShowVoiceInput(false);
  };

  // 对话模式处理函数
  const handleChatSendMessage = async (message: string) => {
    // 添加用户消息
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user' as const,
      content: message,
      timestamp: Date.now()
    };
    setChatMessages(prev => [...prev, userMessage]);
    setChatLoading(true);

    try {
      // 模拟AI回复 - 这里可以集成实际的AI接口
      setTimeout(() => {
        const aiResponses = [
          `我理解您的问题。根据当前的${taskData?.cateName || '巡检'}任务，我建议您：\n\n1. 首先检查相关设备的基本状态\n2. 确认是否符合安全规范\n3. 记录发现的问题\n\n需要我为您提供更详细的指导吗？`,
          `基于您提供的信息，我为您分析如下：\n\n**检查要点：**\n- 设备外观是否完好\n- 功能是否正常\n- 安全标识是否清晰\n\n**处理建议：**\n- 如发现问题，请及时记录\n- 必要时联系相关人员处理`,
          `很好的问题！对于这种情况，建议您：\n\n**立即处理：**\n- 停止相关操作\n- 确保人员安全\n\n**后续跟进：**\n- 详细记录问题\n- 制定整改方案\n- 定期复查验收`,
          `感谢您的反馈。我已经为您生成了针对性的建议。如果您需要更多帮助，可以继续提问，我会根据具体情况为您提供专业指导。`
        ];

        const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];

        const aiMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'assistant' as const,
          content: randomResponse,
          timestamp: Date.now()
        };

        setChatMessages(prev => [...prev, aiMessage]);
        setChatLoading(false);

        // 更新进度
        if (chatCurrentStep < 3) {
          setChatCurrentStep(prev => prev + 1);
          setChatProgress(prev => Math.min(prev + 25, 100));
        }
      }, 1500);
    } catch (error) {
      console.error('发送消息失败:', error);
      setChatLoading(false);
      Toast.show('发送失败，请重试');
    }
  };

  const handleChatVoiceMessage = (audioBlob: Blob) => {
    // 模拟语音转文字
    const voiceMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user' as const,
      content: '[语音消息] 这是一条模拟的语音转文字内容，实际使用时会调用语音识别API',
      timestamp: Date.now()
    };

    setChatMessages(prev => [...prev, voiceMessage]);

    // 触发AI回复
    handleChatSendMessage('语音消息');
  };

  const handleCloseChatDialog = () => {
    setShowChatDialog(false);
    setCurrentMode('fill');
  };

  // 清除所有分析结果
  const handleClearResults = () => {
    setAnalysisResults([]);
    setVoiceAnalysisResults([]);
    setCapturedImages([]);
    Toast.show('已清除所有分析结果');
  };
  

  const handleBack = () => {
    navigate(-1);
  };

  const handleCheckItemClick = (item: any) => {
    console.log('点击检查项目:', item,checkItems);
    // 跳转到检查项目详情页
    sessionStorage.setItem('checkItem', JSON.stringify(item));
    sessionStorage.setItem('prameData', JSON.stringify({newId:id,taskCode:taskData.taskCode}));
    debugger;
    if(item.checkCount === 0) {
      navigate(`/check-item/${item.configItemId}`);
    } else {
      navigate(`/inspection-record/${item.configItemId}`);
    }
  };

  const handleStageClick = (stageId: string) => {
    console.log('点击阶段:', stageId);
    // 从taskData中查找对应的place数据
    sessionStorage.setItem('stageId', stageId);
    const placeData = taskData.placeList?.find((item: any) => item.id === stageId);
    if (placeData) {
      // 创建stage对象
      const selectedStage: InspectionStage = {
        id: placeData.id,
        name: placeData.cateName,
        status: placeData.status ? 'completed' as const : 'pending' as const,
      };
      setCurrentStage(selectedStage);
      setCheckItems(placeData.itemRespVOList || []);
      console.log('checkItems', placeData.itemRespVOList);
    }
  };

  const handleVoiceClick = (event: React.MouseEvent<HTMLButtonElement>,item:any) => {
    // 阻止默认行为
    event.stopPropagation();
    console.log('点击语音');
    setShowVoiceInput(true);
    setClickItem(item);
  };

  const handleCameraClick = (event: React.MouseEvent<HTMLButtonElement>,item:any) => {
    // 阻止默认行为
    event.stopPropagation();
    console.log('点击相机');
    setShowPhotoCapture(true);
    setClickItem(item);

  };

  const handlePatrolBtnClick = () => {
    console.log('点击巡检表');
    // navigate('/patrol-table');
    // completeTask
    apis.ginkgoSystem.completeTask({ taskCode: taskData.taskCode,id:taskData.id }).then((res) => {
      console.log('res', res);
      if(res.code === 0) {
        Toast.show({
          content: '巡检表已提交',
          icon: 'success',
        });
        handleBack();
      }
    });
  };


  useEffect(() => {
    // const taskCode = getUrlParameter('taskCode');
    // console.log('taskCode', taskCode);
    // apis.ginkgoSystem.getDetail({ taskCode,id }).then((res) => {
    //   console.log('res', res);

    //   // 使用函数形式确保taskData已更新
    //   setTaskData((_prevTaskData: any) => {
    //     const newTaskData = res.data;
    //     const newStages = newTaskData.placeList.map((item:any) => {
    //       return {
    //         id: item.id,
    //         name: item.cateName,
    //         status: item.status?'completed':'pending'
    //       }
    //     });
    //     setStages(newStages);
    //     sessionStorage.setItem('taskData', JSON.stringify(newTaskData?.placeList));
    //     // 在taskData更新后，使用新数据调用handleStageClick
    //     if (newTaskData.placeList && newTaskData.placeList.length > 0) {
    //       // 直接处理第一个stage的逻辑
    //       const firstPlace = newTaskData.placeList[0];
    //       const selectedStage: InspectionStage = {
    //         id: firstPlace.id,
    //         name: firstPlace.cateName,
    //         status: firstPlace.status ? 'completed' as const : 'pending' as const
    //       };
    //       setCurrentStage(selectedStage);
    //       setCheckItems(firstPlace.itemRespVOList || []);
    //       sessionStorage.setItem('stageId', firstPlace.id);
    //     }

    //     return newTaskData;
    //   });
    // });
    updateTaskData(true);
  }, []);

  const updateTaskData = (first:boolean = false) => {
    const taskCode = getUrlParameter('taskCode');
    console.log('taskCode', taskCode);
    apis.ginkgoSystem.getDetail({ taskCode,id }).then((res) => {
      console.log('res', res);

      // 使用函数形式确保taskData已更新
      setTaskData((_prevTaskData: any) => {
        const newTaskData = res.data;
        const newStages = newTaskData.placeList.map((item:any) => {
          return {
            id: item.id,
            name: item.cateName,
            status: item.status?'completed':'pending'
          }
        });
        setStages(newStages);
        sessionStorage.setItem('taskData', JSON.stringify(newTaskData?.placeList));
        // 在taskData更新后，使用新数据调用handleStageClick
        if (newTaskData.placeList && newTaskData.placeList.length > 0 && first) {
          // 直接处理第一个stage的逻辑
          const firstPlace = newTaskData.placeList[0];
          const selectedStage: InspectionStage = {
            id: firstPlace.id,
            name: firstPlace.cateName,
            status: firstPlace.status ? 'completed' as const : 'pending' as const
          };
          setCurrentStage(selectedStage);
          setCheckItems(firstPlace.itemRespVOList || []);
          sessionStorage.setItem('stageId', firstPlace.id);
        }

        return newTaskData;
      });
    });
  }



  return (
    <div className="task-detail-container">
      {/* 头部 */}
      <header className="task-detail-header">
        <LeftOutline
          className="back-icon"
          onClick={handleBack}
        />
        <div className="header-content">
          <h1 className="page-title">{taskData.executeTaskName}</h1>
        </div>
        <Button className="patrol-btn" size="small" onClick={handlePatrolBtnClick}>
          巡检表
        </Button>
      </header>

      {/* 模式切换 */}
      <div className="mode-tabs">
        <div
          className={`tab-item ${currentMode === 'fill' ? 'active' : ''}`}
          onClick={() => setCurrentMode('fill')}
        >
          填报模式
        </div>
        <div
          className={`tab-item ${currentMode === 'chat' ? 'active' : ''}`}
          onClick={() => {
            setCurrentMode('chat');
            setShowChatDialog(true);
            // 初始化对话
            if (chatMessages.length === 0) {
              setChatMessages([{
                id: '1',
                type: 'assistant',
                content: `您好！我是智能巡检助手。当前正在进行${taskData?.cateName || '巡检'}任务，请告诉我您遇到的问题或需要帮助的地方。`,
                timestamp: Date.now()
              }]);
            }
          }}
        >
          对话模式
        </div>
      </div>

      <div className="progress-info">
          <span className="progress-text">完成度</span>
          <span className="progress-percent">{taskData.completion}%</span>
      </div>
      {/* 进度条和阶段 */}
      <div className="progress-section">
        <div className="stages-container">
          {stages.map((stage) => (
            <div
              key={stage.id}
              className={`stage-item ${stage.status} ${currentStage?.id === stage.id ? 'active' : ''}`}
              onClick={() => handleStageClick(stage.id)}
            >
              <div className="stage-dot">
                {stage.status === 'completed' && <span className="check-mark"></span>}
                {stage.status === 'current' && <span className="current-dot"></span>}
                {stage.status === 'pending' && <span className="pending-dot"></span>}
              </div>
              <span className="stage-name">{stage.name}</span>
            </div>
          ))}
        </div>
        
      </div>

      {/* 检查项目列表 */}
      <div className="check-items-container">
        {checkItems.map((item) => (
          <div
            key={item.configItemId}
            className="check-item"
            onClick={() => handleCheckItemClick(item)}
          >
            <div className="item-header">
              <div className={`status-badge ${item.status===0?'pending':'completed'}`}>
                {item.status === 0 ? '未完成' : '已完成'}
              </div>
              <span className="item-name">{item.cateName}</span>
              <span className="requirements-count">{item.requirementCount}条检查要求</span>
              <span className="arrow">›</span>
            </div>
            <div className="item-stats">
              <span className="stats-text">
                已检查 {item.checkCount} 处，发现隐患 {item.hazardCount} 个
              </span>
              <div className="action-buttons">
                {item.checkWay?.includes(1) && <Button
                  className="voice-btn"
                  size="mini"
                  fill="outline"
                  onClick={(event) => handleVoiceClick(event,item)}
                >
                    <span className="voice-btn-icon"></span>
                </Button>}
                {item.checkWay?.includes(0) && <Button
                  className="camera-btn"
                  size="mini"
                  fill="outline"
                  onClick={(event) => handleCameraClick(event,item)}
                >
                    <span className="camera-btn-icon"></span>
                </Button>}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 分析结果显示区域 */}

      {/* PhotoCapture 组件 */}
      <PhotoCapture
        visible={showPhotoCapture}
        onClose={handleClosePhotoCapture}
        onImageCaptured={handlePhotoCaptured}
        title="拍照上传"
        quality={0.8}
        maxWidth={1200}
        maxHeight={1200}
      />

      {/* 语音输入组件 */}
      {showVoiceInput && (
        <div className="voice-input-overlay">
          <SpeechRecognitionContainer
            onSend={handleVoiceSend}
            onClose={handleVoiceClose}
          />
        </div>
      )}

      {/* 对话模式组件 */}
      <ChatDialog
        visible={showChatDialog}
        onClose={handleCloseChatDialog}
        title={`${taskData?.cateName || '巡检'}智能助手`}
        progress={chatProgress}
        progressSteps={["问题描述", "情况分析", "建议生成", "方案确认"]}
        currentStep={chatCurrentStep}
        messages={chatMessages}
        onSendMessage={handleChatSendMessage}
        onVoiceMessage={handleChatVoiceMessage}
        loading={chatLoading}
        placeholder="请描述您遇到的问题或需要的帮助..."
        maxLength={300}
        showVoiceButton={true}
        showProgress={true}
      />
    </div>
  );
};

export default TaskDetail;
