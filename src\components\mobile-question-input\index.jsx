/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-28 15:45:18
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-24 20:37:05
 * @FilePath: src/components/mobile-question-input/index.jsx
 * @Version: 1.0.0
 * @Description: 组件描述   问答输入组件
 */
import { apis,projectPath } from '@/api/api';
import delFileSvg from '../../assets/images/chat/icon_del_file.svg';
import pdfSvg from '../../assets/images/chat/pdf.svg';
import wordSvg from '../../assets/images/chat/word.svg';
import SceneType from '../../enums/scene-type';
import { UPLOAD_CONFIG, UPLOAD_CONFIG_IMAGE, UPLOAD_CONFIG_NEW } from '@/util/const';
import { monitorSoftKeyboard } from '@/util/soft-keyboard';
import { Image, message, Popover, Upload } from 'antd';
import { Text<PERSON><PERSON> } from 'antd-mobile';
import classNames from 'classnames';
import { useEffect, useRef, useState } from 'react';
import { LoadingOutlined } from '@ant-design/icons';
import styles from './index.module.scss';


const MobileQuestionInput = ({ sceneType, showStop, onUpdateData, onSend, onChange }) => {
	const [bFocused, setBFocused] = useState(false);    // 文本是否获取焦点
	const [deepThinkingSelected, setDeepThinkingSelected] = useState(false);
	const [onlineSearchSelected, setOnlineSearchSelected] = useState(false);
	const [bVoice, setBVoice] = useState(false);    // 是否语音输入状态
	const [bUploaded, setBUploaded] = useState(false);  // 是否是文件上传状态
	const [disabled, setDisabled] = useState(false);
	const [questions, setQuestions] = useState('');
	const [fileList, setFileList] = useState([]);
	const [placeholder, setPlaceholder] = useState('您有什么问题尽管问我');
	const inputRef = useRef(null);
	const deepBtnRef = useRef(null);
	const onlineBtnRef = useRef(null);
	const sendBtnRef = useRef(null);
	const uploadBtnRef = useRef(null);
	const albumBtnRef = useRef(null);
	const fileBtnRef = useRef(null);
	const uploadFileRef = useRef(null);
	const containerRef = useRef(null);
	const fileListRef = useRef(null);
	const takePicRef = useRef(null);
	const lastHeight = useRef(window.innerHeight);

	const onPressEnter = () => {
		if (questions.length > 0 || fileList.length > 0) {
			onSend();
			setQuestions('');
			setBFocused(false);
			setFileList([]);
		}
	};

	const inputChange = (value) => {
		setQuestions(value);
	};

	const onFocus = () => {
		setBFocused(true);
	};

	const beforeUpload = (file, uploadItem) => {
		const fileExtension = file.name.slice((file.name.lastIndexOf('.') - 1 >>> 0) + 2).toLowerCase();
		console.log('fileExtension:', fileExtension, uploadItem);
		if (!uploadItem.accept.includes(fileExtension)) {
			message.error(`仅支持${uploadItem.accept}文件`);
			return false;
		}
		const size = uploadItem.fileSize || 5;
		if (file.size > size * 1024 * 1024) {
			message.error(`文件上限为${size}M`);
			return false;
		}
		if (sceneType === 'risk_analysis') {
			setPlaceholder('您可以补充照片信息，如：医院消控室、医院工程维修等，我可以为您提供更精准的识别结果');
		}
		setFileList([{
			fileExtension,
			fileName: file.name,
			isUpload: true,
			size: file.size,
			fileParam: uploadItem.fileParam,
			filtType: uploadItem.filtType,
			fileKey: uploadItem.fileKey,
		}]);
		setDisabled(true);
		const formData = new FormData();
		formData.append('file', file);
		formData.append('type', uploadItem.filtType);
		apis.uploadFile.uploadFile(formData).then(res => {
			if (res.code === 0) {
				setFileList([{
					fileExtension,
					...res.data,
					fileId: res.data.id,
					fileParam: uploadItem.fileParam,
					filtType: uploadItem.filtType,
					fileKey: uploadItem.fileKey,
				}]);
				setDisabled(false);
				setBUploaded(false);
				setBFocused(true);
			} else {
				message.error(res.message);
				setFileList([]);
			}
		}).catch((err) => {
			message.error(err.message);
			setFileList([]);
		});
		return false;
	};

	const renderUploadFile = (item, index) => {
		switch (item.filtType) {
			case 'kit': {
				return (
					<div
						key={index}
						className={styles['file-item']}
						title={item.fileName}
					>
						<div className={styles['file-icon']}><img
							src={item.fileExtension.includes('pdf') ? pdfSvg : wordSvg}/>
						</div>
						<div className={styles['file-info']}>
							<div className={styles['file-name']}>{item.fileName}</div>
							{/* <div className={style['file-type-size']}>
					                                        <div>{item.fileExtension.toUpperCase()}</div>
					                                        <div>{item.size ? getFileSize(item.size) : ''}</div>
					                                    </div> */}
						</div>
						<div className={styles['file-del']}>{item.isUpload ?
							<LoadingOutlined/> :
							<img src={delFileSvg} onClick={() => setFileList([])}/>}</div>
					</div>
				);
			}
			case 'image': {
				return <div
					key={index}
					className={styles['file-item']}
					title={item.fileName}
				>
					{
						item.isUpload ? <LoadingOutlined/> :
							<Image
								src={`${projectPath}/upload/downLoadByUrl?url=${item.fileUrl}`}/>
					}
					{
						!item.isUpload &&
						<div className={styles['img-del']}>
							<img
								src={delFileSvg}
								onClick={() => setFileList([])}
								alt={'删除'}
							/>
						</div>
					}
				</div>;
			}
			case 'chat_video':
				return (
					<div className={styles['file-item']} title={item.fileName}>
						{
							item.isUpload ? <LoadingOutlined/> : <video
								src={item.isUpload ? '' : `${projectPath}/upload/downLoadByUrl?url=${item.fileUrl}`}/>
						}
						{
							!item.isUpload && <div className={styles['file-del']}>{<img src={delFileSvg}
							                                                            onClick={() => setFileList([])}/>}</div>
						}
					</div>
				);
			default:
				return null;
		}
	};

	useEffect(() => {
		const onTouchEnd = (e) => {
			if (e.target !== deepBtnRef.current
				&& e.target !== containerRef.current
				&& e.target !== onlineBtnRef.current
				&& e.target !== sendBtnRef.current
				&& e.target !== uploadBtnRef.current
				&& e.target !== albumBtnRef.current
				&& e.target !== fileBtnRef.current
				&& e.target !== takePicRef.current
				&& e.target !== uploadFileRef.current
				&& (inputRef.current && e.target !== inputRef.current.nativeElement)
				&& fileListRef.current.length === 0) {
				setBFocused(false);
				setBUploaded(false);
			}
			// console.log(e.target, e.target.style, takePicRef.current);
		};
		document.addEventListener('touchend', onTouchEnd);

		monitorSoftKeyboard(isUp => {
			// alert(isUp);
			// message.info(`键盘是否弹起:${isUp}`);
		});

		return () => {
			document.removeEventListener('touchend', onTouchEnd);
		};
	}, []);

	useEffect(() => {
		fileListRef.current = fileList;
		onUpdateData({
			questions,
			fileList,
			deepThinkingSelected,
			onlineSearchSelected,
		});
	}, [fileList, questions, deepThinkingSelected, onlineSearchSelected]);

	return (
		<div
			ref={containerRef}
			className={classNames(styles['mobile-question-input-container'], {
				[styles['default-scene']]: bFocused,
				[styles['upload-btn-active']]: bUploaded,
				[styles['disabled']]: showStop,
			})}
		>
			<div
				className={classNames(styles['capsule-tab-wrapper'], {
					[styles['hide']]: fileList.length === 0,
				})}
			>
				{
					fileList.length > 0 && (
						<div
							ref={uploadFileRef}
							className={styles['upload-file']}
						>
							{
								fileList.map((item, index) => renderUploadFile(item, index))
							}
						</div>
					)
				}
				{/*<CapsuleTabs defaultActiveKey={'1'}>
					<CapsuleTabs.Tab title={'视频学习'} key="1"/>
					<CapsuleTabs.Tab title={'编题出卷'} key="2"/>
					<CapsuleTabs.Tab title={'隐患排查'} key="3"/>
					<CapsuleTabs.Tab title={'法律法规'} key="4"/>
					<CapsuleTabs.Tab title={'风险识别'} key="5"/>
				</CapsuleTabs>*/}
			</div>
			<div
				className={styles['mobile-question-input-wrapper']}
			>
				<div
					className={classNames(styles['voice-button'], {
						[styles['voice-active']]: bVoice,
					})}
					onClick={() => {
						setBVoice(!bVoice);
					}}
				/>
				{
					bVoice ? (
						<div
							className={classNames(styles['voice-label'], {
								[styles['active']]: bFocused && (sceneType === SceneType.DEFAULT || sceneType === ''),
							})}
							onClick={() => {
								onChange();
							}}
						>
							按住说话
						</div>
					) : (
						<TextArea
							ref={inputRef}
							rows={1}
							onPressEnter={onPressEnter}
							onFocus={onFocus}
							placeholder={placeholder}
							value={questions}
							bordered={false}
							onChange={inputChange}
							autoSize={{ minRows: 1, maxRows: 2 }}
							maxLength={300}
						/>
					)
				}
				{
					UPLOAD_CONFIG_NEW[sceneType] && (
						<div
							ref={uploadBtnRef}
							className={classNames(styles['upload'], {
								[styles['upload-active']]: bUploaded,
							})}
							onClick={() => {
								setBUploaded(!bUploaded);
							}}
						/>
					)
				}
				<div
					className={classNames(styles['mobile-question-input-focus'], {
						[styles['show']]: bFocused && (sceneType === SceneType.DEFAULT || sceneType === ''),
					})}
				>
					<div
						ref={deepBtnRef}
						className={classNames(styles['btn-item-wrapper'], {
							[styles['selected']]: deepThinkingSelected,
						})}
						onClick={() => {
							setDeepThinkingSelected(!deepThinkingSelected);
						}}
					>
						<div className={classNames(styles['icon'], styles['deep-thinking'])}/>
						<div className={styles['label']}>深度思考</div>
					</div>
					<div
						ref={onlineBtnRef}
						className={classNames(styles['btn-item-wrapper'], {
							[styles['selected']]: onlineSearchSelected,
						})}
						onClick={() => {
							setOnlineSearchSelected(!onlineSearchSelected);
						}}
					>
						<div className={classNames(styles['icon'], styles['online-search'])}/>
						<div className={styles['label']}>联网搜索</div>
					</div>
				</div>
				<div
					ref={sendBtnRef}
					className={classNames(styles['send'], {
						[styles['active']]: questions.length > 0 || fileList.length > 0,
						[styles['show']]: bFocused,
					})}
					onClick={onPressEnter}
				/>
			</div>
			<div
				className={classNames(styles['upload-btn-wrapper'])}
			>
				{
					sceneType !== SceneType.SET_EXAMS && UPLOAD_CONFIG_IMAGE[sceneType] && (
						<Upload
							accept={UPLOAD_CONFIG_IMAGE[sceneType].accept}
							disabled={disabled}
							capture="environment"
							maxCount={1}
							multiple={false}
							fileList={[]}
							beforeUpload={(file) => beforeUpload(file, UPLOAD_CONFIG_IMAGE[sceneType])}
						>
							<div
								ref={takePicRef}
								className={styles['action-btn-wrapper']}
							>
								<div
									className={classNames(styles['action-btn'], styles['take-pic'])}
								>
								</div>
								<div
									className={styles['action-label']}
								>
									拍照
								</div>
							</div>
						</Upload>)
				}
				{
					sceneType !== SceneType.SET_EXAMS &&
					UPLOAD_CONFIG_IMAGE[sceneType] && (
						<Upload
							accept={UPLOAD_CONFIG_IMAGE[sceneType].accept}
							disabled={disabled}
							maxCount={1}
							multiple={false}
							fileList={[]}
							beforeUpload={(file) => beforeUpload(file, UPLOAD_CONFIG_IMAGE[sceneType])}
						>
							<div
								ref={albumBtnRef}
								className={styles['action-btn-wrapper']}
							>
								<div
									className={classNames(styles['action-btn'], styles['album'])}
								></div>
								<div
									className={styles['action-label']}
								>
									相册
								</div>
							</div>
						</Upload>)
				}
				{
					sceneType === SceneType.SET_EXAMS && (
						<Upload
							accept={UPLOAD_CONFIG_NEW[sceneType].accept}
							maxCount={1}
							multiple={false}
							fileList={[]}
							beforeUpload={(file) => beforeUpload(file, UPLOAD_CONFIG_NEW[sceneType])}
						>
							<div
								ref={fileBtnRef}
								className={styles['action-btn-wrapper']}
							>
								<div
									className={classNames(styles['action-btn'], styles['file'])}
								></div>
								<div
									className={styles['action-label']}
								>
									文件
								</div>
							</div>
						</Upload>
					)
				}
				{
					sceneType === SceneType.FIRE_OPERATION && (
						<Upload
							accept={UPLOAD_CONFIG_NEW[sceneType].accept}
							maxCount={1}
							multiple={false}
							fileList={[]}
							beforeUpload={(file) => beforeUpload(file, UPLOAD_CONFIG_NEW[sceneType])}
						>
							<div
								ref={fileBtnRef}
								className={styles['action-btn-wrapper']}
							>
								<div
									className={classNames(styles['action-btn'], styles['file'])}
								></div>
								<div
									className={styles['action-label']}
								>
									视频
								</div>
							</div>
						</Upload>
					)
				}
			</div>
		</div>
	);
};

export default MobileQuestionInput;