/**
 * <AUTHOR>
 * @date 2018/11/9
 * @version:v1.1.0
 */
import axios from 'axios';
import { Toast, Modal } from 'antd-mobile';

const getToken = () => {
    return sessionStorage.getItem("token") || "";
  }
const CancelToken = axios.CancelToken;

let cancel;

let logout = 0; //登出弹窗

let service = axios.create({
    baseURL: '',
    timeout: 15000
});

//添加请求拦截器
service.interceptors.request.use(function (config) {
    let token = getToken();

    if (token) {
        config.headers['token'] = token;
    }/* else if(window.location.hash != "#/login" && !token){
        window.location.hash = "#/login";
        return;
    } */
    return config
}, function (error) {
    return Promise.reject(error)
});

//添加响应拦截器
// axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
axios.interceptors.response.use(function (response) {
    return response;
}, function (error) {
    console.error(error)
    return Promise.reject(error)
}
);

export function commonGet(url, param, tip, deleteToken) {
    let token = getToken();
    //url加时间戳 防止缓存
    let getTimestamp = (new Date()).getTime();
    let tempUrl = '';
    if (url.indexOf("?") > -1) {
        tempUrl = url + "&v=" + getTimestamp;
    } else {
        tempUrl = url + "?v=" + getTimestamp;
    }


    if (!deleteToken) {
        param.token = token
    }

    return new Promise((resolve, reject) => {
        service({
            method: 'get',
            url: tempUrl,
            params: param,
            cancelToken: new CancelToken(c => {
                cancel = c
            })
        }).then(res => {  //axios返回的是一个promise对象

            resolve(res)  //resolve在promise执行器内部
            if (res.data.code === 0) {

                // if (tip) {
                //     Toast.success(tip)
                // }
            } else {
                res.data.message && console.error(res.data.message)
                if (res.data.message === "token失效！") {
                    if (logout > 0) return;
                    logout = 1;
                    Modal.confirm({
                        title: '登陆超时，请重新登录',
                        onConfirm: async () => {
                            let href = window.location.href.split('/#/')[0];
                            window.location.href
                        },
                        // okText: '知道了',
                        // onOk() {
                        //     let href = window.location.href.split('/#/')[0];
                        //     window.location.href = href;
                        // },
                    });
                }
            }
        })
            .catch(err => {
                console.error(err, '异常')
            })

    })
}

//post请求
export function commonPost(url, param, tip, isFile, clearToken) {
    let token = getToken();

    let params;

    if (isFile) {
        params = new FormData();

    } else {
        params = new URLSearchParams();
    }

    for (let i in param) {
        if (param[i] || param[i] == 0) {
            params.append(i, param[i]);
        }

    }
    !clearToken && params.append('token', token);
    return new Promise((resolve, reject) => {
        service({
            method: 'post',
            url,
            data: params
        }).then(res => {
            resolve(res);
            if (res.data.code === 0) {

                // if (tip) {
                //     Toast.show(tip)
                // }
            } else {
                res.data.message && console.error(res.data.message)
                if (res.data.message === "token失效！") {
                    if (logout > 0) return;
                    logout = 1;
                    Modal.confirm({
                        title: '登陆超时，请重新登录',
                        onConfirm: async () => {
                            let href = window.location.href.split('/#/')[0];
                            window.location.href
                        },
                        // okText: '知道了',
                        // onOk() {
                        //     let href = window.location.href.split('/#/')[0];
                        //     window.location.href = href;
                        // },
                    });
                }
            }
        })
            .catch(err => {
                console.error(err, '异常')
            })
    })
}