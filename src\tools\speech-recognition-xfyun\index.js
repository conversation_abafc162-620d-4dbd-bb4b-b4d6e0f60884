/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-14 13:43:36
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-16 17:31:03
 * @FilePath: src/tools/speech-recognition-xfyun/index.js
 * @Version: 1.0.0
 * @Description: 组件描述 科大讯飞 语音转换
 */
// @ts-ignore
import { message } from 'antd';
// @ts-ignore
import CryptoJS from 'crypto-js';

// 讯飞语音
const XF_CONFIG = {
	APPID: '1414c569',
	API_SECRET: 'ZmE2OTMwZDEwMWI5YjQ3MzczMGMxMGYz',
	API_KEY: 'ff3a7e921402bb530aecfb48c60cda33'
};

const getWebSocketUrl = () => {
	// 请求地址根据语种不同变化
	let url = 'wss://iat-api.xfyun.cn/v2/iat';
	const host = 'iat-api.xfyun.cn';
	const apiKey = XF_CONFIG.API_KEY;
	const apiSecret = XF_CONFIG.API_SECRET;
	// const date = new Date().toGMTString();
	const date = new Date().toISOString();
	const algorithm = 'hmac-sha256';
	const headers = 'host date request-line';
	const signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/iat HTTP/1.1`;
	const signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	const signature = CryptoJS.enc.Base64.stringify(signatureSha);
	const authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	const authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};

const toBase64 = (buffer) => {
	let binary = '';
	const bytes = new Uint8Array(buffer);
	const len = bytes.byteLength;
	for (let i = 0; i < len; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	return window.btoa(binary);
};

let countdownInterval;
const countdown = () => {
	let seconds = 60;
	countdownInterval = setInterval(() => {
		seconds = seconds - 1;
		if (seconds <= 0) {
			stopSpeech();
		} else {
			// message.success(`录音倒计时：${seconds} s`);
		}
	}, 1000);
};

let cbFn = null;
let resultStr = '';
let resultStrTemp = '';
const renderResult = (resultData) => {
	let jsonData = JSON.parse(resultData);
	if (jsonData.data && jsonData.data.result) {
		let data = jsonData.data.result;
		let str = '';
		let ws = data.ws;
		for (let i = 0; i < ws.length; i++) {
			str = str + ws[i].cw[0].w;
		}
		// 开启wpgs会有此字段(前提：在控制台开通动态修正功能)
		// 取值为 "apd"时表示该片结果是追加到前面的最终结果；取值为"rpl" 时表示替换前面的部分结果，替换范围为rg字段
		if (data.pgs) {
			if (data.pgs === 'apd') {
				// resultStrTemp同步给resultStr
				resultStr = resultStrTemp;
			}
			// 将结果存储在resultStrTemp中
			resultStrTemp = resultStr + str;
		} else {
			resultStr = resultStr + str;
		}
		cbFn && cbFn(resultStrTemp || resultStr || '');
	}
	if (jsonData.code === 0 && jsonData.data.status === 2) {
		iatWS.close();
	}
	if (jsonData.code !== 0) {
		iatWS.close();
		console.error(jsonData);
	}
};

let iatWS;
let recorder;

/**
 * 清空当前输入的文本
 */
const onClear = () => {
	resultStrTemp = '';
	resultStr = '';
	clearInterval(countdownInterval);
};
/**
 * 管理控件
 */
const createRecorderManager = ({ resolve, stop }) => {
	cbFn = resolve;
	recorder = new window.RecorderManager('/dist');
	recorder.onFrameRecorded = ({ isLastFrame, frameBuffer }) => {
		if (iatWS.readyState === iatWS.OPEN) {
			iatWS.send(
				JSON.stringify({
					data: {
						status: isLastFrame ? 2 : 1,
						format: 'audio/L16;rate=16000',
						encoding: 'raw',
						audio: toBase64(frameBuffer),
					},
				})
			);
			if (isLastFrame) {
				stop(resultStrTemp || resultStr || '');
				clearInterval(countdownInterval);
			}
		}
	};
	recorder.onStop = () => {
		stop(resultStrTemp || resultStr || '');
		clearInterval(countdownInterval);
	};
};

/**
 * 建立WebSocket连接，一条语音是一个WS连接
 * @param value 上一次的转换结果
 */
const connectWebSocket = (value) => {
	if ('WebSocket' in window) {
		iatWS = new WebSocket(getWebSocketUrl());
	} else {
		alert('浏览器不支持WebSocket');
		return;
	}
	iatWS.onopen = (e) => {
		console.log('开始录音');
		resultStrTemp = resultStr = value;
		countdown();
		// 开始录音
		recorder.start({
			sampleRate: 16000,
			frameSize: 1280,
		});
		const params = {
			common: {
				app_id: XF_CONFIG.APPID,
			},
			business: {
				language: 'zh_cn',
				domain: 'iat',
				accent: 'mandarin',
				vad_eos: 5000,
				dwa: 'wpgs',
			},
			data: {
				status: 0,
				format: 'audio/L16;rate=16000',
				encoding: 'raw',
			},
		};
		iatWS.send(JSON.stringify(params));
	};
	iatWS.onmessage = (e) => {
		renderResult(e.data);
	};
	iatWS.onerror = (e) => {
		console.error(e);
		stopSpeech();
	};
	iatWS.onclose = (e) => {
		stopSpeech();
	};
};

/**
 * 结束录音
 */
const stopSpeech = () => {
	console.log('结束录音');
	clearInterval(countdownInterval);
	recorder && recorder.stop();
};
/**
 * 销毁
 */
const destroy = () => {
	cbFn = null;
	recorder = null;
	onClear();
};

export {
	createRecorderManager,
	connectWebSocket,
	stopSpeech,
	onClear,
	destroy
};